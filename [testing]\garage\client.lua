local isInGarageZone = false
local garageOpen = false

-- Create blip
Citizen.CreateThread(function()
    local blip = AddBlipForCoord(Config.GarageLocation.menu.x, Config.GarageLocation.menu.y, Config.GarageLocation.menu.z)
    SetBlipSprite(blip, 357)
    SetBlipDisplay(blip, 4)
    SetBlipScale(blip, 0.8)
    SetBlipColour(blip, 3)
    SetBlipAsShortRange(blip, true)
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentString("Garage")
    EndTextCommandSetBlipName(blip)
end)

-- Main thread
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        local playerPed = PlayerPedId()
        local playerCoords = GetEntityCoords(playerPed)
        local distance = #(playerCoords - Config.GarageLocation.menu)
        
        if distance < Config.DrawDistance then
            -- Draw marker
            DrawMarker(
                27, -- Marker type 27
                Config.GarageLocation.menu.x, Config.GarageLocation.menu.y, Config.GarageLocation.menu.z - 1.0,
                0.0, 0.0, 0.0,
                0.0, 0.0, 0.0,
                Config.MarkerSize.x, Config.MarkerSize.y, Config.MarkerSize.z,
                Config.MarkerColor.r, Config.MarkerColor.g, Config.MarkerColor.b, Config.MarkerColor.a,
                false, true, 2, nil, nil, false
            )
            
            if distance < Config.InteractDistance then
                if not isInGarageZone then
                    isInGarageZone = true
                end
                
                -- Draw 3D text above marker
                local x, y, z = table.unpack(Config.GarageLocation.menu)
                DrawText3D(x, y, z + 0.25, "[~b~G~w~] Garage")
                
                if IsControlJustPressed(0, 47) and not garageOpen then -- G key
                    OpenGarage()
                end
            else
                if isInGarageZone then
                    isInGarageZone = false
                end
            end
        else
            Citizen.Wait(500)
        end
    end
end)

function DrawText3D(x, y, z, text)
    local onScreen, _x, _y = World3dToScreen2d(x, y, z)
    local px, py, pz = table.unpack(GetGameplayCamCoords())
    
    SetTextScale(0.35, 0.35)
    SetTextFont(4)
    SetTextProportional(1)
    SetTextColour(255, 255, 255, 215)
    SetTextOutline()
    SetTextEntry("STRING")
    SetTextCentre(1)
    AddTextComponentString(text)
    DrawText(_x, _y)
end

function OpenGarage()
    garageOpen = true
    SetNuiFocus(true, true)
    SendNUIMessage({
        action = 'openGarage',
        vehicles = Config.Vehicles
    })
end

function CloseGarage()
    garageOpen = false
    SetNuiFocus(false, false)
    SendNUIMessage({
        action = 'closeGarage'
    })
end

-- NUI Callbacks
RegisterNUICallback('closeGarage', function(data, cb)
    CloseGarage()
    cb('ok')
end)

RegisterNUICallback('spawnVehicle', function(data, cb)
    local model = data.model
    local spawnPos = Config.GarageLocation.spawn.pos
    local spawnHeading = Config.GarageLocation.spawn.heading
    
    -- Check if spawn area is clear
    if IsAnyVehicleNearPoint(spawnPos.x, spawnPos.y, spawnPos.z, 3.0) then
        TriggerEvent('chat:addMessage', {
            color = { 255, 0, 0},
            multiline = true,
            args = {"Garage", "Spawn area is blocked!"}
        })
        cb('blocked')
        return
    end
    
    RequestModel(model)
    while not HasModelLoaded(model) do
        Citizen.Wait(100)
    end
    
    local vehicle = CreateVehicle(model, spawnPos.x, spawnPos.y, spawnPos.z, spawnHeading, true, false)
    SetEntityAsMissionEntity(vehicle, true, true)
    SetVehicleOnGroundProperly(vehicle)
    SetVehicleNumberPlateText(vehicle, "GARAGE")
    
    -- Put player in vehicle
    TaskWarpPedIntoVehicle(PlayerPedId(), vehicle, -1)
    
    TriggerEvent('chat:addMessage', {
        color = { 0, 255, 0},
        multiline = true,
        args = {"Garage", "Vehicle spawned!"}
    })
    CloseGarage()
    cb('success')
end)

-- Close garage with ESC
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        if garageOpen then
            if IsControlJustPressed(0, 322) then -- ESC key
                CloseGarage()
            end
        else
            Citizen.Wait(500)
        end
    end
end)