-- client.lua

ESX = nil
local lastPos        = nil
local cam            = nil
local menuOpened     = false
local selecting      = false
local isDead         = false

-- 1) Acquire ESX and wait for your character to load
Citizen.CreateThread(function()
  while ESX == nil do
    TriggerEvent('esx:getSharedObject', function(o) ESX = o end)
    Citizen.Wait(100)
  end

  RegisterNetEvent('esx:playerLoaded')
  AddEventHandler('esx:playerLoaded', function()
    -- fetch last position
    ESX.TriggerServerCallback('spawnselector:getLastPosition', function(pos)
      lastPos = pos

      if lastPos == nil then
        -- First time loading in — spawn at default
        local spawn = Config.DefaultSpawn
        local ped = PlayerPedId()
        SetEntityCoords(ped, spawn.pos.x, spawn.pos.y, spawn.pos.z)
        SetEntityHeading(ped, spawn.heading or 0.0)
        FreezeEntityPosition(ped, false)
      else
        -- Returning player — show spawn selector
        OpenSpawnMenu()
      end
    end)
  end)
end)


-- 2) Show the spawn menu & 100m bird’s-eye camera
function OpenSpawnMenu()
  if menuOpened then return end
  menuOpened = true
  selecting  = true

  local ped     = PlayerPedId()
  local jobData = ESX.GetPlayerData().job
  local spawn   = Config.JobSpawns[jobData.name] or Config.DefaultSpawn
  local target  = lastPos or spawn.pos

  -- Freeze player
  FreezeEntityPosition(ped, true)

  -- Camera 100m up
  cam = CreateCam('DEFAULT_SCRIPTED_CAMERA', true)
  SetCamCoord(cam, target.x, target.y, target.z + Config.BirdseyeHeight)
  PointCamAtCoord(cam, target.x, target.y, target.z)
  SetCamActive(cam, true)
  RenderScriptCams(true, false, 0, true, true)

  -- Determine RP name
  local pd = ESX.GetPlayerData()
  local charinfo = pd.charinfo
  if type(charinfo) == 'string' then charinfo = json.decode(charinfo) end
  local playerName = GetPlayerName(ped)
  if charinfo and charinfo.firstname and charinfo.lastname then
    playerName = charinfo.firstname .. ' ' .. charinfo.lastname
  elseif pd.firstname and pd.lastname then
    playerName = pd.firstname .. ' ' .. pd.lastname
  elseif pd.name then
    playerName = pd.name
  end

  -- Show UI
  SetNuiFocus(true, true)
  SendNUIMessage({
    action      = 'show',
    playerName  = playerName,
    jobLogo     = 'img/default.png', -- Always use default.png (same as void.png)
    jobCompound = jobData.label .. ' Compound'
  })
end

-- 3) Handle the menu click
RegisterNUICallback('spawnSelection', function(data, cb)
  -- teardown UI & camera
  SendNUIMessage({ action = 'hide' })
  SetNuiFocus(false, false)
  RenderScriptCams(false, false, 0, true, true)
  if DoesCamExist(cam) then DestroyCam(cam, false) end
  FreezeEntityPosition(PlayerPedId(), false)
  selecting = false

  -- choose dest
  local dest = Config.DefaultSpawn
  if data.option == 'last' then
    dest = { pos = lastPos or Config.DefaultSpawn.pos, heading = Config.DefaultSpawn.heading }
  elseif data.option == 'job' then
    local j = Config.JobSpawns[ ESX.GetPlayerData().job.name ]
    if j then dest = j end
  end

  -- teleport
  local ped = PlayerPedId()
  SetEntityCoords(ped, dest.pos.x, dest.pos.y, dest.pos.z + 1.0)
  SetEntityHeading(ped, dest.heading)

  cb('ok')
end)

-- 4) Keep the camera locked & block controls while menu is open
Citizen.CreateThread(function()
  while true do
    Citizen.Wait(0)
    if selecting and cam and DoesCamExist(cam) then
      RenderScriptCams(true, false, 0, true, true)
      DisableAllControlActions(0)
      EnableControlAction(0, 200, true) -- allow ESC
    end
  end
end)

-- 5) DEATH-RESPAWN LOGIC (instant, no beep)
Citizen.CreateThread(function()
  while true do
    Citizen.Wait(0)
    local ped = PlayerPedId()

    if IsEntityDead(ped) then
      if not isDead then
        isDead = true
      end

      -- display help text without sound
      BeginTextCommandDisplayHelp("STRING")
      AddTextComponentString("Press ~INPUT_CONTEXT~ to respawn at your job spawn")
      EndTextCommandDisplayHelp(0, false, false, -1)

      -- on E press
      if IsControlJustReleased(0, 38) then -- 38 = E
        local jobName = ESX.GetPlayerData().job.name
        local info    = Config.JobSpawns[jobName] or Config.DefaultSpawn
        local pos     = info.pos or info
        local heading = info.heading or 0

        -- revive & teleport
        ResurrectPed(ped)
        ClearPedTasksImmediately(ped)
        SetEntityCoords(ped, pos.x, pos.y, pos.z + 1.0)
        SetEntityHeading(ped, heading)

        isDead = false

        -- ─────── 𝗔𝗗𝗗 𝗧𝗛𝗜𝗦 𝗕𝗟𝗢𝗖𝗞 ───────
        if ESX and ESX.PlayerData and type(ESX.PlayerData) == 'table' then
            ESX.PlayerData.dead = false
        elseif ESX and ESX.GetPlayerData then
            local pd = ESX.GetPlayerData()
            if pd then pd.dead = false end
        end

        TriggerEvent('esx:onPlayerSpawn')
        -- ───────────────────────────────────

      end
    end
  end
end)



Citizen.CreateThread(function()
    local pos = vector3(-1906.7616, 2044.1643, 140.7386)

    while true do
        Wait(0)
        local plyCoords = GetEntityCoords(PlayerPedId())

        if #(plyCoords - pos) < 50.0 then
            -- Draw a blue cylinder marker (type 2) at ground‐level
            DrawMarker(
                25,                      -- Marker type 2
    pos.x, pos.y, pos.z - 0.98,     -- lower the marker 0.8m below your text pos
                0.0, 0.0, 0.0,          -- direction
                0.0, 0.0, 0.0,          -- rotation
                1.5, 1.5, 1.5,          -- scale
                0, 0, 255, 175,         -- blue, semi‐opaque
                false, false, 2,        -- bobUpAndDown, faceCamera, p19
                false, nil, nil, false  -- rotate, textureDict, textureName, drawOnEnts
            )

            -- World→screen for text at 1.4m high
            local onScreen, x, y = World3dToScreen2d(pos.x, pos.y, pos.z + 0.1)
            if onScreen then
                -- Smaller white text with black outline
                SetTextScale(0.4, 0.4)            -- reduced from 1.1
                SetTextFont(4)
                SetTextProportional(true)
                SetTextCentre(true)
                SetTextColour(255, 255, 255, 255)  -- white
                SetTextEdge(2, 0, 0, 0, 255)       -- black outline
                SetTextEntry("STRING")
                AddTextComponentString("/report for gang roles")
                DrawText(x, y)
            end
        end
    end
end)
