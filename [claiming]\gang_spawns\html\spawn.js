// html/spawn.js
let selectedOption = null;

window.addEventListener('message', e => {
  if (e.data.action === 'show') {
    // HEADER
    const logo = document.getElementById('job-logo');
    logo.onerror = () => { 
      // if job-logo.png is missing, fall back
      logo.onerror = null;      // prevent loops
      logo.src = 'img/1.png';
    };
    logo.src = e.data.jobLogo;

    document.getElementById('player-name').textContent  = e.data.playerName;
    document.getElementById('job-compound').textContent = e.data.jobCompound;

    // OPTIONS
    const confirmBtn = document.querySelector('.btn-confirm');
    const locButtons = Array.from(document.querySelectorAll('.btn-last, .btn-job'));

    // reset
    selectedOption = null;
    confirmBtn.classList.remove('enabled');
    confirmBtn.classList.add('disabled');
    confirmBtn.onclick = null;
    locButtons.forEach(el => el.classList.remove('selected'));

    // wire clicks
    locButtons.forEach(el => {
      el.onclick = () => {
        locButtons.forEach(x => x.classList.remove('selected'));
        el.classList.add('selected');
        selectedOption = el.dataset.option;

        confirmBtn.classList.remove('disabled');
        confirmBtn.classList.add('enabled');
        confirmBtn.onclick = () => sendSelection(selectedOption);
      };
    });

    // show menu
    document.getElementById('spawn-menu').style.display = 'block';
  }

  if (e.data.action === 'hide') {
    document.getElementById('spawn-menu').style.display = 'none';
  }
});

function sendSelection(option) {
  fetch(`https://${GetParentResourceName()}/spawnSelection`, {
    method: 'POST',
    headers: { 'Content-Type':'application/json' },
    body: JSON.stringify({ option })
  });
}

