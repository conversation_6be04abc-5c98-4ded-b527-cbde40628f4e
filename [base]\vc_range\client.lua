-- client.lua
-- Range viewer client-side logic

ESX = nil
local isUIOpen = false
local currentMarker = nil
local currentRange = 0

-- Initialize ESX
Citizen.CreateThread(function()
    while ESX == nil do
        TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)
        Citizen.Wait(10)
    end
end)

-- Event to open the UI
RegisterNetEvent('vc_range:openUI')
AddEventHandler('vc_range:openUI', function()
    if not isUIOpen then
        openRangeUI()
    end
end)

-- Function to open the range UI
function openRangeUI()
    isUIOpen = true
    SetNuiFocus(true, true)
    SendNUIMessage({
        type = 'openUI'
    })
end

-- Function to close the range UI
function closeRangeUI()
    isUIOpen = false
    SetNuiFocus(false, false)
    SendNUIMessage({
        type = 'closeUI'
    })
end

-- NUI Callbacks
RegisterNUICallback('closeUI', function(data, cb)
    closeRangeUI()
    cb('ok')
end)

RegisterNUICallback('confirmRange', function(data, cb)
    local range = tonumber(data.range)
    if range then
        createRangeMarker(range)
        currentRange = range
        ESX.ShowNotification('Range marker set to ' .. range .. 'm', 'success')
    end
    cb('ok')
end)

RegisterNUICallback('endRange', function(data, cb)
    removeRangeMarker()
    ESX.ShowNotification('Range marker removed', 'info')
    cb('ok')
end)

-- Function to create range marker
function createRangeMarker(range)
    -- Remove existing marker first
    removeRangeMarker()
    
    local playerPed = PlayerPedId()
    local coords = GetEntityCoords(playerPed)
    
    -- Create a marker that follows the player
    currentMarker = {
        range = range,
        active = true
    }
    
    -- Start the marker drawing thread
    Citizen.CreateThread(function()
        while currentMarker and currentMarker.active do
            local playerPed = PlayerPedId()
            local coords = GetEntityCoords(playerPed)
            
            -- Draw the range circle marker
            -- Using marker type 27 (circle), blue color with 50% transparency
            DrawMarker(
                1, -- Marker type (circle)
                coords.x, coords.y, coords.z + -50.0, -- Position (very high)
                0.0, 0.0, 0.0, -- Direction
                0.0, 0.0, 0.0, -- Rotation
                range * 2.0, range * 2.0, 100.0, -- Scale (diameter)
                244, 0, 255, 255, -- RGBA (blue with 50% transparency)
                false, -- Bob up and down
                true, -- Face camera
                2, -- Rotate
                false, -- Texture dict
                nil, -- Texture name
                false -- Draw on entities
            )
            
            Citizen.Wait(0)
        end
    end)
end

-- Function to remove range marker
function removeRangeMarker()
    if currentMarker then
        currentMarker.active = false
        currentMarker = nil
        currentRange = 0
    end
end

-- Clean up on resource stop
AddEventHandler('onResourceStop', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        removeRangeMarker()
        if isUIOpen then
            closeRangeUI()
        end
    end
end)
