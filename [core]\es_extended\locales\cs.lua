Locales["cs"] = {
    -- Inventory
    ["inventory"] = "Inventář ( Váha %s / %s )",
    ["use"] = "Použít",
    ["give"] = "Darovat",
    ["remove"] = "Odhodit",
    ["return"] = "Vrátit",
    ["give_to"] = "Darováno",
    ["amount"] = "Počet",
    ["giveammo"] = "Podat náboje",
    ["amountammo"] = "Počet nábojů",
    ["noammo"] = "Nedostatek!",
    ["gave_item"] = "Daroval %sx %s pro %s",
    ["received_item"] = "Získáno %sx %s od %s",
    ["gave_weapon"] = "Předání %s pro %s",
    ["gave_weapon_ammo"] = "Darování ~o~%sx %s do %s pro %s",
    ["gave_weapon_withammo"] = "Darování %s s ~o~%sx %s pro %s",
    ["gave_weapon_hasalready"] = "%s již vlastní %s",
    ["gave_weapon_noweapon"] = "%s nemá tuto zbraň",
    ["received_weapon"] = "Obdrženo %s od %s",
    ["received_weapon_ammo"] = "Obdrženo ~o~%sx %s pro zbraň %s od %s",
    ["received_weapon_withammo"] = "Obdrženo %s s ~o~%sx %s od %s",
    ["received_weapon_hasalready"] = "%s se snažil darovat %s, ale již tuto zbraň máš",
    ["received_weapon_noweapon"] = "%s se snažil ti dát náboje %s, ale nemáš potřebnou zbrań",
    ["gave_account_money"] = "Darováno $%s (%s) pro %s",
    ["received_account_money"] = "Získáno $%s (%s) od %s",
    ["amount_invalid"] = "Špatné množství",
    ["players_nearby"] = "Žádný hráč není poblíž",
    ["ex_inv_lim"] = "Nelze sebrat,protože máš plné kapsy %s",
    ["imp_invalid_quantity"] = "Neplatné množství",
    ["imp_invalid_amount"] = "Nelze provést, neplatné množství",
    ["threw_standard"] = "Zahozeno %sx %s",
    ["threw_account"] = "Zahozeno $%s %s",
    ["threw_weapon"] = "Zahozeno %s",
    ["threw_weapon_ammo"] = "Zahozeno %s s ~o~%sx %s",
    ["threw_weapon_already"] = "Již vlastníš tuto zbraň",
    ["threw_cannot_pickup"] = "Kapsy máš plné, nemůžeš sebrat!",
    ["threw_pickup_prompt"] = "Zmáčkni E pro sebrání!",

    -- Key mapping
    ["keymap_showinventory"] = "Otevřít inventář",

    -- Salary related
    ["received_salary"] = "Obdržel jsi: $%s",
    ["received_help"] = "Obdržel jsi svůj podíl: $%s",
    ["company_nomoney"] = "Firma kde pracujete je příliš chudá, aby vám zaplatila",
    ["received_paycheck"] = "Obdržena platba",
    ["bank"] = "Banka",
    ["account_bank"] = "V bance",
    ["account_black_money"] = "Špináve peníze",
    ["account_money"] = "V kapse",

    ["act_imp"] = "Nelze provést",
    ["in_vehicle"] = "Nelze provést, hráč je v autě",
    ["not_in_vehicle"] = "Cannot Perform Action, Player isn't in a vehicle",

    -- Commands
    ["command_bring"] = "Přivolat si hráče k sobě",
    ["command_car"] = "Spawnout vozidlo",
    ["command_car_car"] = "Zadej jméno vozidla nebo spawnname",
    ["command_cardel"] = "Odstranění vozidla v okolí",
    ["command_cardel_radius"] = "Odstranění vozidla v určeném dosahu",
    ["command_repair"] = "Repair your vehicle",
    ["command_repair_success"] = "Successfully repaired vehicle",
    ["command_repair_success_target"] = "An admin repaired your vehicle",
    ["command_clear"] = "Vymazat text v chatu",
    ["command_clearall"] = "Vymazat chet pro všechny hráče",
    ["command_clearinventory"] = "Vymazat všechny věci z invetáře hráče",
    ["command_clearloadout"] = "Vymazat všechny zbraně z inventáře hráče",
    ["command_freeze"] = "Zmrazit hráče",
    ["command_unfreeze"] = "Odmrazit hráče",
    ["command_giveaccountmoney"] = "Poslat peníze na účet ",
    ["command_giveaccountmoney_account"] = "Převést peníze na účet",
    ["command_giveaccountmoney_amount"] = "Částka k poslání",
    ["command_giveaccountmoney_invalid"] = "Neplátné jméno",
    ["command_giveitem"] = "Darovat věc hráči",
    ["command_giveitem_item"] = "Název věci",
    ["command_giveitem_count"] = "Množství",
    ["command_giveweapon"] = "Dát zbraň hráči",
    ["command_giveweapon_weapon"] = "Název zbraně",
    ["command_giveweapon_ammo"] = "Množství náboje",
    ["command_giveweapon_hasalready"] = "Hráč již má tuto zbraň",
    ["command_giveweaponcomponent"] = "Darovat přídavek na zbraň",
    ["command_giveweaponcomponent_component"] = "Název zbraně",
    ["command_giveweaponcomponent_invalid"] = "Špatné jméno přídavku",
    ["command_giveweaponcomponent_hasalready"] = "Hráč již má tento přídavek",
    ["command_giveweaponcomponent_missingweapon"] = "Hráč nemá tuto zbrań",
    ["command_goto"] = "Teleportování sebe k hráči",
    ["command_kill"] = "Zabití hráče",
    ["command_save"] = "Uložení dat hráče",
    ["command_saveall"] = "Uložení veškerých dat hráče",
    ["command_setaccountmoney"] = "Nastavení určeného počtu peněz",
    ["command_setaccountmoney_amount"] = "Počet peněz",
    ["command_setcoords"] = "Teleportování na určené souřadnice",
    ["command_setcoords_x"] = "Hodnota X",
    ["command_setcoords_y"] = "Hodnota Y",
    ["command_setcoords_z"] = "Hodnota Z",
    ["command_setjob"] = "Nastavit práci hráči",
    ["command_setjob_job"] = "Název práce",
    ["command_setjob_grade"] = "Pozice ve firmě",
    ["command_setjob_invalid"] = "Špatné zadání práce,hodnosti nebo i obou hodnot",
    ["command_setgroup"] = "Nastavení práv hráči",
    ["command_setgroup_group"] = "Název skupiny",
    ["commanderror_argumentmismatch"] = "Chybný počet hodnot (správně %s, potřebných %s)",
    ["commanderror_argumentmismatch_number"] = "Chybně zadaná hodnot #%s  (správně, špatně)",
    ["commanderror_argumentmismatch_string"] = "Invalid Argument #%s data type (passed number, wanted string)",
    ["commanderror_invaliditem"] = "Špatný předmět",
    ["commanderror_invalidweapon"] = "Špatná zbraň",
    ["commanderror_console"] = "příkaz nelze být zpracován v konzoli",
    ["commanderror_invalidcommand"] = "Špatný příkaz - /%s",
    ["commanderror_invalidplayerid"] = "Hráč není dostupný",
    ["commandgeneric_playerid"] = "Id hráče",
    ["command_giveammo_noweapon_found"] = "%s nemá tuto zbraň",
    ["command_giveammo_weapon"] = "Název zbraně",
    ["command_giveammo_ammo"] = "Počet nábojů",

    -- Locale settings
    ["locale_digit_grouping_symbol"] = ",",
    ["locale_currency"] = "£%s",

    -- Weapons

    -- Melee
    ["weapon_dagger"] = "Dýka",
    ["weapon_bat"] = "Baseballová pálka",
    ["weapon_battleaxe"] = "Bitevní sekera",
    ["weapon_bottle"] = "Rozbitá lahve",
    ["weapon_crowbar"] = "Páčidlo",
    ["weapon_flashlight"] = "Baterka",
    ["weapon_golfclub"] = "Golfová hůl",
    ["weapon_hammer"] = "Kladivo",
    ["weapon_hatchet"] = "Sekera",
    ["weapon_knife"] = "Nůž",
    ["weapon_knuckle"] = "Boxer",
    ["weapon_machete"] = "Mačeta",
    ["weapon_nightstick"] = "Policejní obušek",
    ["weapon_wrench"] = "Francouzský klíč",
    ["weapon_poolcue"] = "Kulečníkové tágo",
    ["weapon_stone_hatchet"] = "Kamenná sekera",
    ["weapon_switchblade"] = "Vystřelovací nůž",

    -- Handguns
    ["weapon_appistol"] = "AP pistol",
    ["weapon_ceramicpistol"] = "Ceramic pistol",
    ["weapon_combatpistol"] = "Combat pistol",
    ["weapon_doubleaction"] = "Double-Action Revolver",
    ["weapon_navyrevolver"] = "Navy Revolver",
    ["weapon_flaregun"] = "Flaregun",
    ["weapon_gadgetpistol"] = "Gadget Pistol",
    ["weapon_heavypistol"] = "Heavy Pistol",
    ["weapon_revolver"] = "Heavy Revolver",
    ["weapon_revolver_mk2"] = "Heavy Revolver MK2",
    ["weapon_marksmanpistol"] = "Marksman Pistol",
    ["weapon_pistol"] = "Pistol",
    ["weapon_pistol_mk2"] = "Pistol MK2",
    ["weapon_pistol50"] = "Pistol .50",
    ["weapon_snspistol"] = "SNS Pistol",
    ["weapon_snspistol_mk2"] = "SNS Pistol MK2",
    ["weapon_stungun"] = "Taser",
    ["weapon_raypistol"] = "Up-N-Atomizer",
    ["weapon_vintagepistol"] = "Vintage Pistol",

    -- Shotguns
    ["weapon_assaultshotgun"] = "Assault Shotgun",
    ["weapon_autoshotgun"] = "Auto Shotgun",
    ["weapon_bullpupshotgun"] = "Bullpup Shotgun",
    ["weapon_combatshotgun"] = "Combat Shotgun",
    ["weapon_dbshotgun"] = "Double Barrel Shotgun",
    ["weapon_heavyshotgun"] = "Heavy Shotgun",
    ["weapon_musket"] = "Musket",
    ["weapon_pumpshotgun"] = "Pump Shotgun",
    ["weapon_pumpshotgun_mk2"] = "Pump Shotgun MK2",
    ["weapon_sawnoffshotgun"] = "Sawed Off Shotgun",

    -- SMG & LMG
    ["weapon_assaultsmg"] = "Assault SMG",
    ["weapon_combatmg"] = "Combat MG",
    ["weapon_combatmg_mk2"] = "Combat MG MK2",
    ["weapon_combatpdw"] = "Combat PDW",
    ["weapon_gusenberg"] = "Gusenberg Sweeper",
    ["weapon_machinepistol"] = "Machine Pistol",
    ["weapon_mg"] = "MG",
    ["weapon_microsmg"] = "Micro SMG",
    ["weapon_minismg"] = "Mini SMG",
    ["weapon_smg"] = "SMG",
    ["weapon_smg_mk2"] = "SMG MK2",
    ["weapon_raycarbine"] = "Unholy Hellbringer",

    -- Rifles
    ["weapon_advancedrifle"] = "Advanced Rifle",
    ["weapon_assaultrifle"] = "Assault Rifle",
    ["weapon_assaultrifle_mk2"] = "Assault Rifle MK2",
    ["weapon_bullpuprifle"] = "Bullpup Rifle",
    ["weapon_bullpuprifle_mk2"] = "Bullpup Rifle MK2",
    ["weapon_carbinerifle"] = "Carbine Rifle",
    ["weapon_carbinerifle_mk2"] = "Carbine Rifle MK2",
    ["weapon_compactrifle"] = "Compact Rifle",
    ["weapon_militaryrifle"] = "Military Rifle",
    ["weapon_specialcarbine"] = "Special Carbine",
    ["weapon_specialcarbine_mk2"] = "Special Carbine MK2",
    ["weapon_heavyrifle"] = "Heavy Rifle", -- Not Translated

    -- Sniper
    ["weapon_heavysniper"] = "Heavy Sniper",
    ["weapon_heavysniper_mk2"] = "Heavy Sniper MK2",
    ["weapon_marksmanrifle"] = "Marksman Rifle",
    ["weapon_marksmanrifle_mk2"] = "Marksman Rifle MK2",
    ["weapon_sniperrifle"] = "Sniper Rifle",

    -- Heavy / Launchers
    ["weapon_compactlauncher"] = "Compact Launcher",
    ["weapon_firework"] = "Firework Launcher",
    ["weapon_grenadelauncher"] = "Grenade Launcher",
    ["weapon_hominglauncher"] = "Homing Launcher",
    ["weapon_minigun"] = "Minigun",
    ["weapon_railgun"] = "Railgun",
    ["weapon_rpg"] = "Rocket Launcher",
    ["weapon_rayminigun"] = "Widowmaker",

    -- Criminal Enterprises DLC
    ["weapon_metaldetector"] = "Detektor kovu",
    ["weapon_precisionrifle"] = "Precision Rifle",
    ["weapon_tactilerifle"] = "Service Carbine",

    -- Drug Wars DLC
    ["weapon_candycane"] = "Candy Cane", -- not translated
    ["weapon_acidpackage"] = "Acid Package", -- not translated
    ["weapon_pistolxm3"] = "WM 29 Pistol", -- not translated
    ["weapon_railgunxm3"] = "Railgun", -- not translated

    -- Thrown
    ["weapon_ball"] = "Míček",
    ["weapon_bzgas"] = "Smrtící slzný plyn",
    ["weapon_flare"] = "Světlice",
    ["weapon_grenade"] = "Granát",
    ["weapon_petrolcan"] = "Kanistr",
    ["weapon_hazardcan"] = "Hazardous Jerrycan",
    ["weapon_molotov"] = "Molotův koktejl",
    ["weapon_proxmine"] = "Pohybová mina",
    ["weapon_pipebomb"] = "Trubková bomba",
    ["weapon_snowball"] = "Sněhová koule",
    ["weapon_stickybomb"] = "C4",
    ["weapon_smokegrenade"] = "Slzný plyn",

    -- Special
    ["weapon_fireextinguisher"] = "Hasící přístroj",
    ["weapon_digiscanner"] = "Skener",
    ["weapon_garbagebag"] = "Odpadkový pytel",
    ["weapon_handcuffs"] = "Pouta",
    ["gadget_nightvision"] = "Noční vidění",
    ["gadget_parachute"] = "Padák",

    -- Weapon Components
    ["component_knuckle_base"] = "base Model",
    ["component_knuckle_pimp"] = "the Pimp",
    ["component_knuckle_ballas"] = "the Ballas",
    ["component_knuckle_dollar"] = "the Hustler",
    ["component_knuckle_diamond"] = "the Rock",
    ["component_knuckle_hate"] = "the Hater",
    ["component_knuckle_love"] = "the Lover",
    ["component_knuckle_player"] = "the Player",
    ["component_knuckle_king"] = "the King",
    ["component_knuckle_vagos"] = "the Vagos",

    ["component_luxary_finish"] = "luxary Weapon Finish",

    ["component_handle_default"] = "default Handle",
    ["component_handle_vip"] = "vIP Handle",
    ["component_handle_bodyguard"] = "bodyguard Handle",

    ["component_vip_finish"] = "vIP Finish",
    ["component_bodyguard_finish"] = "bodyguard Finish",

    ["component_camo_finish"] = "digital Camo",
    ["component_camo_finish2"] = "brushstroke Camo",
    ["component_camo_finish3"] = "woodland Camo",
    ["component_camo_finish4"] = "skull Camo",
    ["component_camo_finish5"] = "sessanta Nove Camo",
    ["component_camo_finish6"] = "perseus Camo",
    ["component_camo_finish7"] = "leopard Camo",
    ["component_camo_finish8"] = "zebra Camo",
    ["component_camo_finish9"] = "geometric Camo",
    ["component_camo_finish10"] = "boom Camo",
    ["component_camo_finish11"] = "patriotic Camo",

    ["component_camo_slide_finish"] = "digital Slide Camo",
    ["component_camo_slide_finish2"] = "brushstroke Slide Camo",
    ["component_camo_slide_finish3"] = "woodland Slide Camo",
    ["component_camo_slide_finish4"] = "skull Slide Camo",
    ["component_camo_slide_finish5"] = "sessanta Nove Slide Camo",
    ["component_camo_slide_finish6"] = "perseus Slide Camo",
    ["component_camo_slide_finish7"] = "leopard Slide Camo",
    ["component_camo_slide_finish8"] = "zebra Slide Camo",
    ["component_camo_slide_finish9"] = "geometric Slide Camo",
    ["component_camo_slide_finish10"] = "boom Slide Camo",
    ["component_camo_slide_finish11"] = "patriotic Slide Camo",

    ["component_clip_default"] = "default Magazine",
    ["component_clip_extended"] = "extended Magazine",
    ["component_clip_drum"] = "drum Magazine",
    ["component_clip_box"] = "box Magazine",

    ["component_scope_holo"] = "holographic Scope",
    ["component_scope_small"] = "small Scope",
    ["component_scope_medium"] = "medium Scope",
    ["component_scope_large"] = "large Scope",
    ["component_scope"] = "mounted Scope",
    ["component_scope_advanced"] = "advanced Scope",
    ["component_ironsights"] = "ironsights",

    ["component_suppressor"] = "suppressor",
    ["component_compensator"] = "compensator",

    ["component_muzzle_flat"] = "flat Muzzle Brake",
    ["component_muzzle_tactical"] = "tactical Muzzle Brake",
    ["component_muzzle_fat"] = "fat-End Muzzle Brake",
    ["component_muzzle_precision"] = "precision Muzzle Brake",
    ["component_muzzle_heavy"] = "heavy Duty Muzzle Brake",
    ["component_muzzle_slanted"] = "slanted Muzzle Brake",
    ["component_muzzle_split"] = "split-End Muzzle Brake",
    ["component_muzzle_squared"] = "squared Muzzle Brake",

    ["component_flashlight"] = "flashlight",
    ["component_grip"] = "grip",

    ["component_barrel_default"] = "default Barrel",
    ["component_barrel_heavy"] = "heavy Barrel",

    ["component_ammo_tracer"] = "tracer Ammo",
    ["component_ammo_incendiary"] = "incendiary Ammo",
    ["component_ammo_hollowpoint"] = "hollowpoint Ammo",
    ["component_ammo_fmj"] = "fMJ Ammo",
    ["component_ammo_armor"] = "armor Piercing Ammo",
    ["component_ammo_explosive"] = "armor Piercing Incendiary Ammo",

    ["component_shells_default"] = "default Shells",
    ["component_shells_incendiary"] = "dragons Breath Shells",
    ["component_shells_armor"] = "steel Buckshot Shells",
    ["component_shells_hollowpoint"] = "flechette Shells",
    ["component_shells_explosive"] = "explosive Slug Shells",

    -- Weapon Ammo
    ["ammo_rounds"] = "round(s)",
    ["ammo_shells"] = "shell(s)",
    ["ammo_charge"] = "charge",
    ["ammo_petrol"] = "gallons of fuel",
    ["ammo_firework"] = "firework(s)",
    ["ammo_rockets"] = "rocket(s)",
    ["ammo_grenadelauncher"] = "grenade(s)",
    ["ammo_grenade"] = "grenade(s)",
    ["ammo_stickybomb"] = "bomb(s)",
    ["ammo_pipebomb"] = "bomb(s)",
    ["ammo_smokebomb"] = "bomb(s)",
    ["ammo_molotov"] = "cocktail(s)",
    ["ammo_proxmine"] = "mine(s)",
    ["ammo_bzgas"] = "can(s)",
    ["ammo_ball"] = "ball(s)",
    ["ammo_snowball"] = "snowball(s)",
    ["ammo_flare"] = "flare(s)",
    ["ammo_flaregun"] = "flare(s)",

    -- Weapon Tints
    ["tint_default"] = "default skin",
    ["tint_green"] = "green skin",
    ["tint_gold"] = "gold skin",
    ["tint_pink"] = "pink skin",
    ["tint_army"] = "army skin",
    ["tint_lspd"] = "blue skin",
    ["tint_orange"] = "orange skin",
    ["tint_platinum"] = "platinum skin",
    -- MK2 Weapon Tints
    ["tint_classic_gray"] = "klasická šedá",
    ["tint_classic_two_tone"] = "klasická dvoutónová",
    ["tint_classic_white"] = "klasická bílá",
    ["tint_classic_beige"] = "klasická béžová",
    ["tint_classic_green"] = "klasická zelená",
    ["tint_classic_blue"] = "klasická modrá",
    ["tint_classic_earth"] = "klasická hnědá",
    ["tint_classic_brown_black"] = "klasická hnědá-černá",
    ["tint_contrast_red"] = "kontrastní červená",
    ["tint_contrast_blue"] = "kontrastní modrá",
    ["tint_contrast_yellow"] = "kontrastní žlutá",
    ["tint_contrast_orange"] = "kontrastní oranžová",
    ["tint_bold_pink"] = "odvážná růžová",
    ["tint_bold_purple_yellow"] = "odvážná fialovo-žlutá",
    ["tint_bold_orange"] = "odvážná oranžová",
    ["tint_bold_green_purple"] = "odvážná zeleno-fialová",
    ["tint_bold_red_feat"] = "odvážná červená feat",
    ["tint_bold_green_feat"] = "odvážná zelená feat",
    ["tint_bold_cyan_feat"] = "odvážná azurová feat",
    ["tint_bold_yellow_feat"] = "odvážná žlutá feat",
    ["tint_bold_red_white"] = "odvážná červená bílá",
    ["tint_bold_blue_white"] = "odvážná modrá bílá",
    ["tint_metallic_gold"] = "metalická zlatá",
    ["tint_metallic_platinum"] = "metalická platina",
    ["tint_metallic_gray_lilac"] = "metalická šedá lila",
    ["tint_metallic_purple_lime"] = "metalická fialová limetka",
    ["tint_metallic_red"] = "metalická červená",
    ["tint_metallic_green"] = "metalická zelená",
    ["tint_metallic_blue"] = "metalická modrá",
    ["tint_metallic_white_aqua"] = "metalická bílá aqua",
    ["tint_metallic_red_yellow"] = "metalická červená žlutá",
}
