local isLoadingScreenFinished = false

RegisterNetEvent("esx_identity:alreadyRegistered", function()
    while not isLoadingScreenFinished do Wait(100) end

    TriggerEvent("esx_skin:playerRegistered")
end)

RegisterNetEvent("esx_identity:setPlayerData", function(data)
    if type(data) ~= "table" then return end

    Wait(10)

    ESX.SetPlayerData("name", ("%s %s"):format(data.firstName, data.lastName))
    ESX.SetPlayerData("firstName", data.firstName)
    ESX.SetPlayerData("lastName", data.lastName)
    ESX.SetPlayerData("dateofbirth", data.dateOfBirth)
    ESX.SetPlayerData("sex", data.sex)
    ESX.SetPlayerData("height", data.height)
end)

AddEventHandler("esx:loadingScreenOff", function()
    isLoadingScreenFinished = true
end)

if Config.UseDeferrals then return end

local function showIdentityForm()
    SetTimecycleModifier("hud_def_blur")
    SetNuiFocus(true, true)

    SendNUIMessage({
        type = 'enableui'
    })
end

RegisterNUICallback('register', function(data, cb)
    ESX.TriggerServerCallback("esx_identity:registerIdentity", function(callback)
        if callback then
            ClearTimecycleModifier()
            SetNuiFocus(false, false)

            SendNUIMessage({
                type = 'disableui'
            })

            ESX.ShowNotification(_U("thank_you_for_registering"), "success")

            if ESX.GetConfig().Multichar then return end

            TriggerEvent("esx_skin:playerRegistered")
        else
            ESX.ShowNotification(_U("registration_error"), "error")
        end
    end, data)

    cb('ok')
end)

RegisterNUICallback('cancel', function(data, cb)
    -- Just close the UI, don't do anything else
    ClearTimecycleModifier()
    SetNuiFocus(false, false)

    SendNUIMessage({
        type = 'disableui'
    })

    cb('ok')
end)

RegisterNetEvent("esx_identity:showRegisterIdentity", function()
    TriggerEvent("esx_skin:resetFirstSpawn")

    if not ESX.PlayerData.dead then showIdentityForm() end
end)
