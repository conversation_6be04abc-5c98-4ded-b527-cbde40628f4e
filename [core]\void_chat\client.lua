diff --git a/client.lua b/client.lua
index e69de29..b123456 100644
--- a/client.lua
+++ b/client.lua
@@ -1,6 +1,12 @@
- -- Hide default chat and forward messages to our NUI
+ -- Hide default chat and forward messages to our NUI
 AddEventHandler('chatMessage', function(author, color, text)
   CancelEvent()
   SendNUIMessage({
     type   = 'chatMessage',
     author = author,
     color  = color,
     text   = text
   })
 end)
+
+-- Hide and clear the default chat UI on resource start
+AddEventHandler('onClientResourceStart', function(resName)
+  if resName == GetCurrentResourceName() then
+    TriggerEvent('chat:clear')
+    TriggerEvent('chat:display', false)
+    TriggerEvent('chat:toggleChat', false)
+    TriggerEvent('chat:toggleVisible', false)
+  end
+end)
@@ -15,7 +21,7 @@ end)
 
 -- Open custom chat input on T (control 245)
 Citizen.CreateThread(function()
-  if IsControlJustReleased(0, 20) then -- 20 is default INPUT_MULTIPLAYER_INFO
+  if IsControlJustReleased(0, 245) then -- 245 is native “open chat” (T)
     SendNUIMessage({ type = 'openInput' })
     SetNuiFocus(true, true)
   end
