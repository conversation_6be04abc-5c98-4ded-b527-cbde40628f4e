<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>SAFE ZONE</title>
  <style>
    html, body {
      margin: 0;
      padding: 0;
      background: transparent;
      overflow: hidden;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    #safeZoneUI {
      position: absolute;
      top: 20px;
      left: 50%;
      transform: translateX(-50%);
      background: rgba(60, 60, 60, 0.9);
      color: white;
      padding: 10px 18px;
      border-radius: 12px;
      font-size: 16px;
      font-weight: 600;
      z-index: 9999;
      animation: slideDown 0.5s ease-out;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
      display: none;
      align-items: center;
      justify-content: center;
      gap: 8px;
    }

    #safeZoneUI img {
      width: 24px;
      height: 24px;
      object-fit: contain;
    }



    @keyframes slideDown {
      from {
        opacity: 0;
        transform: translateX(-50%) translateY(-20px);
      }
      to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
      }
    }

    #safeZoneUI.show {
      display: flex;
      animation: slideDown 0.5s ease-out;
    }

    #safeZoneUI.hide {
      animation: slideUp 0.3s ease-in forwards;
    }

    @keyframes slideUp {
      from {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
      }
      to {
        opacity: 0;
        transform: translateX(-50%) translateY(-20px);
      }
    }
  </style>
</head>
<body>
  <div id="safeZoneUI">
    <span>SAFE ZONE</span>
  </div>

  <script>
    window.addEventListener('message', function (event) {
      const box = document.getElementById('safeZoneUI');

      if (event.data && event.data.action === 'showSafeZone') {
        box.className = 'show';
        box.style.display = 'flex';
      } else if (event.data && event.data.action === 'hideSafeZone') {
        box.className = 'hide';
        setTimeout(() => {
          box.style.display = 'none';
          box.className = '';
        }, 300);
      }
    });
  </script>
</body>
</html>
