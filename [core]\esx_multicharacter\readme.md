<h1 align='center'>[ESX] Multi-Character</a></h1><p align='center'><b><a href='https://discord.esx-framework.org/'>Discord</a> - <a href='https://esx-framework.org/'>Website</a> - <a href='https://docs.esx-legacy.com/'>Documentation</a></b></h5>

A Simplistic system, that allows Players to have multiple Characters, which can be customised for all player with `Config.Slots` or personally set a players character count using `setslots`, `remslots`, `enablechar` and `disablechar` Commands :)

![Preview](https://i.imgur.com/EAHImD0.png)

## Notes

- Characters are stored in the users table as `char#:license`
- Character deletion does not require manual entries for the tables to remove

## Kashacters

- This project is forked from the [Kashacters resource](https://github.com/FiveEYZ/esx_kashacter)
- Most of the code has been entirely rewritten
- <PERSON><PERSON><PERSON> has given permission for this resource to use his code and the addition of a license
- The license obviously does not apply to previous versions and <PERSON><PERSON><PERSON> has stated his resource is free to be used however

<br>
<table><tr><td><h4 align='center'>Legal Notices</h4></tr></td>
<tr><td>
Official Multi-Character system for ESX Legacy

Copyright © 2022-2024 Linden, ESX-Framework and KASH

This program is free software: you can redistribute it and/or modify
it under the terms of the GNU General Public License as published by
the Free Software Foundation, either version 3 of the License, or
(at your option) any later version.

This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
GNU General Public License for more details.

You should have received a copy of the GNU General Public License
along with this program.  If not, see <https://www.gnu.org/licenses>.
</td></tr></table>
