$(document).ready(function() {
    // Hide UI initially
    $('#range-container').addClass('hidden');
    
    // Listen for messages from the client
    window.addEventListener('message', function(event) {
        const data = event.data;
        
        if (data.type === 'openUI') {
            $('#range-container').removeClass('hidden');
        } else if (data.type === 'closeUI') {
            $('#range-container').addClass('hidden');
        }
    });
    
    // Confirm button click
    $('#confirm-btn').click(function() {
        const selectedRange = $('#range-select').val();
        
        // Send the selected range to the client
        $.post('https://vc_range/confirmRange', JSON.stringify({
            range: selectedRange
        }));
        
        // Close the UI
        closeUI();
    });
    
    // End button click
    $('#end-btn').click(function() {
        // Send end range request to client
        $.post('https://vc_range/endRange', JSON.stringify({}));
        
        // Close the UI
        closeUI();
    });
    
    // Close UI function
    function closeUI() {
        $('#range-container').addClass('hidden');
        
        // Notify client that UI is closed
        $.post('https://vc_range/closeUI', JSON.stringify({}));
    }
    
    // Close UI when clicking outside the panel
    $('#range-container').click(function(e) {
        if (e.target === this) {
            closeUI();
        }
    });
    
    // Close UI with Escape key
    $(document).keyup(function(e) {
        if (e.keyCode === 27) { // Escape key
            if (!$('#range-container').hasClass('hidden')) {
                closeUI();
            }
        }
    });
});
