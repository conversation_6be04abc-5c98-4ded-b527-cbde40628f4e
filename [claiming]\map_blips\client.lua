-- client.lua - CLEAN SAFE ZONE SYSTEM v2.0.0
if IsDuplicityVersion() then return end

print("^2[MAP_BLIPS] Safe Zone System v2.0.0 - Client Loading...^7")

ESX = nil
local inSafeZone = false
local currentZone = nil

-- Get ESX object
Citizen.CreateThread(function()
  while ESX == nil do
    TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)
    Citizen.Wait(0)
  end
end)

-- Define safe zones
local safeZones = {
  { name = "Brothers MC",   coord = vector3(342.57,  -1096.14, 29.40), radius = 30.0, colour = 1 },
  { name = "The Muzz Hand", coord = vector3(-198.58, -1716.36, 32.66), radius = 30.0, colour = 52 },
  { name = "<PERSON><PERSON><PERSON>",         coord = vector3(-319.91, -1087.79, 23.02), radius = 30.0, colour = 4 },
  { name = "Lost MC",       coord = vector3(972.86,  -120.48,  74.35), radius = 35.0, colour = 40 },
  { name = "Adlays",        coord = vector3(38.37,   -900.57,  29.99), radius = 30.0, colour = 7 },
  { name = "Void",         coord = vector3(-43.4385, -1099.3450, 26.4223), radius = 35.0, colour = 3 },
  { name = "Server Boosters", coord = vector3(-491.9352, -54.7387, 39.9940), radius = 30.0, colour = 8 },    -- pink
  { name = "Infamous",         coord = vector3(0.6223,  -1305.5929, 30.1959), radius = 35.0,  colour = 50 },    -- purple
  { name = "Nockas",           coord = vector3(335.5846, -1391.3060, 32.5093), radius = 30.0, colour = 38 }, -- dark blue
  { name = "Santos Cartel",    coord = vector3(-732.4092, -412.1743, 35.2950), radius = 30.0, colour = 16 },  -- tan
  { name = "United Bloods",    coord = vector3(413.5562, -1493.0447, 30.1490), radius = 30.0, colour = 6 },   -- dark red
  { name = "187 Mob",          coord = vector3(12.8326, -1073.8368, 38.1522), radius = 30.0, colour = 1 },    -- red
  { name = "TRODG",            coord = vector3(251.3158, -788.4469, 30.4417), radius = 30.0, colour = 40 },     -- black
  { name = "Vagos",            coord = vector3(41.9417, -598.0667, 31.6286), radius = 30.0, colour = 5 },      -- yellow 
  { name = "Virello Familia",  coord = vector3(238.4904, -1779.0963, 28.7089), radius = 30.0, colour = 69 },   -- lime green
  { name = "Gooners MC",       coord = vector3(-62.3238, -1836.9177, 26.7401), radius = 30.0, colour = 52 },   -- dark green
  { name = "Niggers MC",       coord = vector3(471.1424, -1095.1730, 29.2021), radius = 30.0, colour = 40 },   -- black 
  { name = "DRF",              coord = vector3(442.8676, -1004.8042, 31.5231), radius = 30.0, colour = 40 }, -- black 
  { name = "La Rosa Nera Mafia ", coord = vector3(-557.1479, -1793.3049, 22.4530), radius = 30.0, colour = 1 }, -- red 
  { name = "Yurei",            coord = vector3(-374.0546, -117.8281, 38.6970), radius = 30.0, colour = 4 }, -- pink 
  { name = "Dirty Mums",       coord = vector3(441.6064, 226.1206, 103.1655), radius = 30.0, colour = 48 }, -- pink 
  { name = "Ronin",            coord = vector3(-606.9885, 192.7249, 70.6256), radius = 30.0, colour = 66 }, -- yellow 
  { name = "Lowkey Thieves",  coord = vector3(-201.6398, -1301.3074, 31.2960), radius = 30.0, colour = 52 }, -- green
  { name = "Black Monkey MC", coord = vector3(-320.4735, -696.4664, 33.0187), radius = 30.0, colour = 67 }, -- light blue
  { name = "Sutaro Crime Family", coord = vector3(-628.3765, -1202.4882, 13.8934), radius = 30.0, colour = 59 }, -- red
  { name = "Celestials MC",   coord = vector3(-708.7961, -869.1238, 23.4151), radius = 30.0, colour = 8 }, -- pink -
  { name = "Raptor",          coord = vector3(-16.6291, -696.1578, 32.3381), radius = 30.0, colour = 25 }, -- green
  { name = "Cursed",              coord = vector3(-203.5785, -806.2724, 30.4540), radius = 30.0, colour = 16 }, -- cream
}


-- Create blips at startup
Citizen.CreateThread(function()
  Citizen.Wait(1000)
  print("^3[MAP_BLIPS] Creating safe zone blips...^7")

  for _, zone in ipairs(safeZones) do
    -- radius circle
    local r = AddBlipForRadius(zone.coord.x, zone.coord.y, zone.coord.z, zone.radius)
    SetBlipColour(r, zone.colour)
    SetBlipAlpha(r, 120)
    SetBlipDisplay(r, 2)
    SetBlipAsShortRange(r, false)
    SetBlipHighDetail(r, true)

    -- center icon + name
    local b = AddBlipForCoord(zone.coord.x, zone.coord.y, zone.coord.z)
    SetBlipSprite(b, 84)
    SetBlipColour(b, zone.colour)
    SetBlipScale(b, 0.9)
    SetBlipDisplay(b, 2)
    SetBlipAsShortRange(b, false)
    SetBlipHighDetail(b, true)
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentString(zone.name)
    EndTextCommandSetBlipName(b)

    print(string.format("^2[MAP_BLIPS] Created safe zone: %s^7", zone.name))
  end

  -- Create gang blips
  print("^3[MAP_BLIPS] Creating gang blips...^7")

  for _, gang in ipairs(gangBlips) do
    -- Create gang blip
    local b = AddBlipForCoord(gang.coord.x, gang.coord.y, gang.coord.z)
    SetBlipSprite(b, 84)  -- Gang icon
    SetBlipColour(b, gang.colour)
    SetBlipScale(b, 0.8)
    SetBlipDisplay(b, 2)
    SetBlipAsShortRange(b, false)
    SetBlipHighDetail(b, true)
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentString(gang.name)
    EndTextCommandSetBlipName(b)

    print(string.format("^2[MAP_BLIPS] Created gang blip: %s^7", gang.name))
  end

  print("^2[MAP_BLIPS] Safe Zone System v2.0.0 - FULLY LOADED!^7")
end)

-- Main safe zone detection and management
Citizen.CreateThread(function()
  while true do
    Citizen.Wait(500)
    local ped = PlayerPedId()
    local pos = GetEntityCoords(ped)
    local foundZone, name = false, nil

    for _, zone in ipairs(safeZones) do
      if #(pos - zone.coord) <= zone.radius then
        foundZone = true
        name = zone.name
        break
      end
    end

    if foundZone and not inSafeZone then
      -- Entering safe zone
      inSafeZone = true
      currentZone = name

      -- Make player invincible to all damage types
      SetEntityInvincible(ped, true)
      SetEntityCanBeDamaged(ped, false)
      SetPedCanRagdoll(ped, false)
      SetEntityProofs(ped, true, true, true, true, true, true, true, true)

      SendNUIMessage({ action = 'showSafeZone', zoneName = name })

      -- Disable any sounds
      StopAudioScene("CHARACTER_CHANGE_IN_SKY_SCENE")
      StopAudioScene("SAFEHOUSE_MICHAEL_SCENE")

      -- Notify server
      TriggerServerEvent('map_blips:playerEnteredSafeZone', name)

      print(string.format('[SAFE ZONE CLIENT] Entered: %s', name))
    elseif not foundZone and inSafeZone then
      -- Leaving safe zone
      inSafeZone = false
      currentZone = nil

      -- Remove all protections
      SetEntityInvincible(ped, false)
      SetEntityCanBeDamaged(ped, true)
      SetPedCanRagdoll(ped, true)
      SetEntityProofs(ped, false, false, false, false, false, false, false, false)

      SendNUIMessage({ action = 'hideSafeZone' })

      -- Disable any sounds
      StopAudioScene("CHARACTER_CHANGE_IN_SKY_SCENE")
      StopAudioScene("SAFEHOUSE_MICHAEL_SCENE")

      -- Notify server
      TriggerServerEvent('map_blips:playerLeftSafeZone')

      print('[SAFE ZONE CLIENT] Left safe zone')
    end
  end
end)

-- Block combat when inside safe zone
Citizen.CreateThread(function()
  while true do
    Citizen.Wait(0)
    if inSafeZone then
      -- Disable all combat controls
      DisableControlAction(0, 24, true)   -- Attack
      DisableControlAction(0, 25, true)   -- Aim
      DisableControlAction(0, 257, true)  -- Melee attack
      DisableControlAction(0, 263, true)  -- Melee attack 2
      DisableControlAction(0, 264, true)  -- Melee attack 3
      DisableControlAction(0, 140, true)  -- Light melee attack
      DisableControlAction(0, 141, true)  -- Heavy melee attack
      DisableControlAction(0, 142, true)  -- Alternate melee attack
      DisableControlAction(0, 143, true)  -- Block
      DisablePlayerFiring(PlayerId(), true)

      -- Disable weapon/combat sounds
      SetAudioFlag("DisableFlightMusic", true)
      SetAudioFlag("PoliceScannerDisabled", true)

      -- Help text removed to keep UI clean
    else
      -- Re-enable audio flags when not in safe zone
      SetAudioFlag("DisableFlightMusic", false)
      SetAudioFlag("PoliceScannerDisabled", false)
    end
  end
end)

-- Maintain damage protection in safe zones (backup protection)
Citizen.CreateThread(function()
  while true do
    Citizen.Wait(100)
    if inSafeZone then
      local ped = PlayerPedId()

      -- Clear any damage but don't heal
      ClearEntityLastDamageEntity(ped)

      -- Ensure all protections are maintained
      if not GetEntityInvincible(ped) then
        SetEntityInvincible(ped, true)
        SetEntityCanBeDamaged(ped, false)
        SetPedCanRagdoll(ped, false)
        SetEntityProofs(ped, true, true, true, true, true, true, true, true)
      end
    end
  end
end)
