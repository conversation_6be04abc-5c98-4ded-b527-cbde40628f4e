document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('identity-form');

    // Form validation
    function validateForm() {
        let isValid = true;

        // Clear previous errors
        document.querySelectorAll('.error').forEach(el => {
            el.classList.remove('show');
            el.textContent = '';
        });

        // First name validation
        const firstname = document.getElementById('firstname').value.trim();
        if (!firstname || firstname.length < 2) {
            showError('firstname-error', 'First name must be at least 2 characters');
            isValid = false;
        } else if (!/^[a-zA-Z\s]+$/.test(firstname)) {
            showError('firstname-error', 'First name can only contain letters');
            isValid = false;
        }

        // Last name validation
        const lastname = document.getElementById('lastname').value.trim();
        if (!lastname || lastname.length < 2) {
            showError('lastname-error', 'Last name must be at least 2 characters');
            isValid = false;
        } else if (!/^[a-zA-Z\s]+$/.test(lastname)) {
            showError('lastname-error', 'Last name can only contain letters');
            isValid = false;
        }

        // Date of birth validation
        const dob = document.getElementById('dob').value;
        if (!dob) {
            showError('dob-error', 'Please select your date of birth');
            isValid = false;
        }

        // Height validation
        const height = document.getElementById('height').value;
        if (!height || isNaN(height)) {
            showError('height-error', 'Please enter a valid height');
            isValid = false;
        }

        // Gender validation
        const gender = document.querySelector('input[name="gender"]:checked');
        if (!gender) {
            showError('gender-error', 'Please select your gender');
            isValid = false;
        }

        return isValid;
    }

    function showError(elementId, message) {
        const errorElement = document.getElementById(elementId);
        errorElement.textContent = message;
        errorElement.classList.add('show');
    }
    
    // Form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();

        if (validateForm()) {
            const submitBtn = document.querySelector('.btn-confirm');
            const originalText = submitBtn.textContent;

            // Show loading state
            submitBtn.textContent = 'CREATING...';
            submitBtn.disabled = true;
            submitBtn.style.opacity = '0.7';

            const formData = new FormData(form);

            // Convert date to timestamp (server expects timestamp)
            const dobString = formData.get('dob');
            const dobDate = new Date(dobString);
            const dobTimestamp = dobDate.getTime();

            const data = {
                firstname: formData.get('firstname').trim(),
                lastname: formData.get('lastname').trim(),
                dateofbirth: dobTimestamp,
                height: parseInt(formData.get('height')),
                sex: formData.get('gender')
            };

            // Send data to FiveM
            fetch(`https://${GetParentResourceName()}/register`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            }).then(response => {
                if (response.ok) {
                    submitBtn.textContent = 'SUCCESS!';
                    setTimeout(() => {
                        document.body.classList.add('hidden');
                    }, 500);
                } else {
                    // Reset button on error
                    submitBtn.textContent = originalText;
                    submitBtn.disabled = false;
                    submitBtn.style.opacity = '1';
                }
            }).catch(error => {
                console.error('Error:', error);
                // Reset button on error
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
                submitBtn.style.opacity = '1';
            });
        }
    });
    
    // Real-time validation
    document.getElementById('firstname').addEventListener('input', function() {
        const error = document.getElementById('firstname-error');
        if (error.classList.contains('show')) {
            error.classList.remove('show');
        }
    });

    document.getElementById('lastname').addEventListener('input', function() {
        const error = document.getElementById('lastname-error');
        if (error.classList.contains('show')) {
            error.classList.remove('show');
        }
    });

    document.getElementById('dob').addEventListener('change', function() {
        const error = document.getElementById('dob-error');
        if (error.classList.contains('show')) {
            error.classList.remove('show');
        }
    });

    document.getElementById('height').addEventListener('input', function() {
        const error = document.getElementById('height-error');
        if (error.classList.contains('show')) {
            error.classList.remove('show');
        }
    });

    document.querySelectorAll('input[name="gender"]').forEach(input => {
        input.addEventListener('change', function() {
            const error = document.getElementById('gender-error');
            if (error.classList.contains('show')) {
                error.classList.remove('show');
            }
        });
    });
    
    // Listen for NUI messages
    window.addEventListener('message', function(event) {
        const data = event.data;

        if (data.type === 'enableui') {
            document.body.classList.remove('hidden');
            // Focus first input for better UX
            setTimeout(() => {
                document.getElementById('firstname').focus();
            }, 300);
        } else if (data.type === 'disableui') {
            document.body.classList.add('hidden');
        }
    });
});

// Cancel form function
function cancelForm() {
    fetch(`https://${GetParentResourceName()}/cancel`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({})
    });
}

function GetParentResourceName() {
    return window.location.hostname === 'nui-game' ? 'esx_identity' : 'esx_identity';
}
