<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Void Range Viewer</title>
    <link rel="stylesheet" href="style.css">
    <script src="nui://game/ui/jquery.js" type="text/javascript"></script>
</head>
<body>
    <div id="range-container" class="hidden">
        <div class="range-panel">
            <div class="header">
                <h1 class="title">Void Range Viewer</h1>
            </div>
            
            <div class="content">
                <div class="range-selection">
                    <label for="range-select">Select Range:</label>
                    <select id="range-select">
                        <option value="150">150m</option>
                        <option value="170">170m</option>
                        <option value="180">180m</option>
                        <option value="190">190m</option>
                        <option value="200" selected>200m</option>
                        <option value="210">210m</option>
                        <option value="220">220m</option>
                    </select>
                </div>
                
                <div class="button-container">
                    <button id="confirm-btn" class="btn btn-confirm">Confirm</button>
                    <button id="end-btn" class="btn btn-end">End</button>
                </div>
            </div>
        </div>
    </div>
    
    <script src="script.js"></script>
</body>
</html>
