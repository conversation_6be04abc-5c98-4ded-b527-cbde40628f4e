
-- Connection Logic

local function AwaitContext()
    while not exports.esx_context do
        Wait(100)
    end
end

CreateThread(function()
    AwaitContext()

    while not ESX.PlayerLoaded do
        Wait(100)

        if NetworkIsPlayerActive(ESX.playerId) then
            ESX.DisableSpawnManager()
            DoScreenFadeOut(0)

            Multicharacter:SetupCharacters()
            break
        end
    end
end)

-- Events

ESX.SecureNetEvent("esx_multicharacter:SetupUI", function(data, slots)
    Multicharacter:SetupUI(data, slots)
    -- notify spawn_selector that we’ve finished character selection
    TriggerEvent('esx:onPlayerSpawn')
end)



RegisterNetEvent('esx:playerLoaded', function(playerData, isNew, skin)
    Multicharacter:PlayerLoaded(playerData, isNew, skin)
end)

ESX.SecureNetEvent('esx:onPlayerLogout', function()
    DoScreenFadeOut(500)
    Wait(5000)

    Multicharacter.spawned = false

    Multicharacter:SetupCharacters()
    TriggerEvent("esx_skin:resetFirstSpawn")
end)

-- Relog

if Config.Relog then
    RegisterCommand("relog", function()
        if Multicharacter.canRelog then
            Multicharacter.canRelog = false
            TriggerServerEvent("esx_multicharacter:relog")

            ESX.SetTimeout(10000, function()
                Multicharacter.canRelog = true
            end)

        end
    end,false)
end

-- NUI Callbacks for new character selection interface

RegisterNUICallback('selectCharacter', function(data, cb)
    print('[MULTICHAR] Character selected via NUI:', data.charid)

    if Multicharacter.Characters[data.charid] then
        Multicharacter:SetupCharacter(data.charid)
    end

    cb('ok')
end)

RegisterNUICallback('createCharacter', function(data, cb)
    print('[MULTICHAR] Creating new character in slot:', data.charid)

    Multicharacter:CloseUI()
    SetNuiFocus(false, false)

    TriggerServerEvent("esx_multicharacter:CharacterChosen", data.charid, true)
    TriggerEvent("esx_identity:showRegisterIdentity")

    cb('ok')
end)

RegisterNUICallback('spawnCharacter', function(data, cb)
    print('[MULTICHAR] Spawning character:', data.charid)

    if Multicharacter.Characters[data.charid] then
        Multicharacter:CloseUI()
        SetNuiFocus(false, false)

        TriggerServerEvent("esx_multicharacter:CharacterChosen", data.charid, false)
    end

    cb('ok')
end)
