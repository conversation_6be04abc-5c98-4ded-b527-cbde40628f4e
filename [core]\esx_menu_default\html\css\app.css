@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@500;600&display=swap");

::-webkit-scrollbar {
    display: none;
}

.menu {
    font-family: "Poppins", sans-serif;
    min-width: 350px;
    color: #fff;
    position: absolute;
    background: rgba(15, 15, 15, 0.0);
    text-align: center;
    border-radius: 5px;
}

/* Garage-specific styling - Target by menu ID */
#menu_garage_garage_menu {
    background: rgb(0, 0, 0) !important; /* Dark background */
    border: 2px solid #000 !important;
    border-radius: 8px !important;
    width: 350px !important;
    position: fixed !important;
    bottom: 50px !important;
    right: 20px !important;
}

.head {
    display: block;
    overflow: hidden;
    padding-bottom: 3px;
    text-align: center;
    white-space: nowrap;
    background: rgb(60, 134, 255);    /* ← this is your red bar */
    border-bottom: 2px solid #ffffff;
}

/* Garage menu container - Bigger size, positioned at bottom right */
#menu_garage_garage_menu {
    position: fixed !important;
    bottom: 5px !important; /* Very close to bottom edge */
    right: 20px !important; /* Right side with margin */
    width: 400px !important; /* Much bigger width */
    max-height: 320px !important; /* Much bigger height */
    background: rgba(0, 0, 0, 0.685) !important; /* Solid grey background */
    border: 1px solid rgba(0, 0, 0, 0.8) !important; /* Thin dark border */
    border-radius: 4px !important; /* Slightly rounded corners */
    padding: 0 !important;
    z-index: 1000 !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important; /* Subtle shadow */
}

/* Garage menu header - Clean, simple */
#menu_garage_garage_menu .head {
    background: rgba(0, 0, 0, 0.151) !important; /* Slightly darker grey */
    color: white !important;
    text-align: center !important;
    padding: 10px 15px !important; /* Bigger padding for larger size */
    font-size: 16px !important; /* Bigger text for larger container */
    font-weight: bold !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.61) !important; /* Subtle separator */
    border-radius: 3px 3px 0 0 !important; /* Slightly rounded top */
    margin: 0 !important;
}

/* Garage menu items container - No extra space */
#menu_garage_garage_menu .menu-items {
    background: transparent !important;
    max-height: none !important;
    overflow-y: visible !important;
    height: auto !important;
    min-height: auto !important;
    padding: 0 !important;
    margin: 0 !important;
}

/* Section headers */
#menu_garage_garage_menu .menu-item.section {
    background: rgba(7, 7, 7, 0.603) !important; /* Darker grey for sections */
    color: white !important;
    text-align: center !important;
    padding: 8px 12px !important; /* Bigger section padding */
    font-size: 14px !important; /* Bigger font for larger container */
    font-weight: bold !important;
    border: none !important;
    margin: 0 !important;
    cursor: default !important; /* Not selectable */
}

/* Regular vehicle items */
#menu_garage_garage_menu .menu-item:not(.section) {
    background: rgba(31, 31, 31, 0.747) !important; /* Grey background */
    color: #5cb0e7 !important; /* Green text for available vehicles */
    text-align: left !important; /* Left align for numbered list */
    padding: 8px 15px !important; /* Bigger padding for larger container */
    font-size: 15px !important; /* Bigger font for better readability */
    border: none !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1) !important; /* Very subtle separator */
    margin: 0 !important;
}

/* Remove border from last item */
#menu_garage_garage_menu .menu-item:last-child {
    border-bottom: none !important;
}

/* Hover effect for selectable items only */
#menu_garage_garage_menu .menu-item:not(.section):hover {
    background: rgba(59, 130, 246, 0.3) !important; /* Blue hover */
}

/* Selected effect for selectable items only */
#menu_garage_garage_menu .menu-item:not(.section).selected {
    background: rgba(59, 130, 246, 0.5) !important; /* Blue selected */
    color: white !important;
}

/* Inactive vehicles (red text) */
#menu_garage_garage_menu .menu-item.inactive {
    color: #f79191 !important; /* Red text for inactive vehicles */
}

/* Inactive vehicles when selected */
#menu_garage_garage_menu .menu-item.inactive.selected {
    background: rgba(220, 38, 38, 0.3) !important; /* Red selected for inactive */
    color: #f79191 !important;
}




.menu .head {
    text-align: center;
    height: 32px;
    color: #ffffff;
    font-weight: 700;
    font-size: 24px;
}

.menu .menu-items {
    max-height: 450px;
    overflow-y: auto;
    font-weight: 550;
}

.menu-items {
    margin-bottom: 2px;
}

.menu .menu-items .menu-item {
    display: block;
    padding: 10px;
    font-size: 16px;
    height: 16px;
    text-indent: 5px;
    background: rgba(0, 0, 0, 0.65);
    color: rgb(243, 243, 243);
}

/* Garage menu items - available vehicles (green) */
.garage-menu .menu-items .menu-item {
    background: rgba(61, 61, 61, 0.8); /* Grey background */
    color: #9af791; /* Green text for available vehicles */
    border: 1px solid #000;
    margin-bottom: 2px;
}

/* Garage menu items - inactive vehicles (red) */
.garage-menu .menu-items .menu-item.inactive {
    color: #f79191; /* Red text for inactive vehicles */
}


.menu .menu-items .menu-item.selected {
    border: 1px solid #ffffff;
    background: rgba(0, 0, 0, 0.65);
    color: rgba(243, 243, 243);
    letter-spacing: 0.2px;
    font-weight: 500;
}

/* Garage menu selected items */
.garage-menu .menu-items .menu-item.selected {
    border: 2px solid #fff;
    background: rgba(100, 100, 100, 0.9); /* Slightly lighter grey when selected */
}

/* Garage menu selected inactive items */
.garage-menu .menu-items .menu-item.selected.inactive {
    color: #f79191; /* Keep red color even when selected */
}

.menu.align-left {
    left: 0;
    top: 50%;
}

.menu.align-top-left {
    left: 4rem;
    top: 0;
}

.menu.align-top {
    left: 50%;
    top: 0;
}

.menu.align-top-right {
    right: 3rem;
    top: 0;
}

.menu.align-right {
    right: 0;
    top: 50%;
}

.menu.align-bottom-right {
    right: 3rem;
    bottom: 0;
}

.menu.align-bottom {
    left: 50%;
    bottom: 0;
    transform: translate(0, -50%);
}

.menu.align-bottom-left {
    left: 4rem;
    bottom: 0;
}

.menu.align-center {
    left: 50%;
    top: 35%;
    transform: translate(-50%, 50%);
}
