-- config.lua

Config = {}

-- Allow any job to start a claim
Config.AllowAnyJob = true

-- Preset claim locations
Config.Locations = {
  ['1'] = { coords = vector3(-521.5204, -689.6874, 33.1680), label = "Cluckin' Bell" },
  ['2'] = { coords = vector3(146.3413, -1044.6799, 29.3778), label = 'Legion Bank' },
  ['3'] = { coords = vector3(-128.0703, -1585.1348, 32.2820), label = 'Southside apartments' },
  ['4'] = { coords = vector4(810.0594, -2156.8237, 29.6190, 355.6623), label = 'South side gun store' },
  ['5'] = { coords = vector4(389.6968, -355.9053, 48.0244, 273.1632), label = 'Market' },
  ['6']  = { coords = vector4(-1261.9875, -364.7209, 36.9596, 295.4836), label = 'Gym' },
['7']  = { coords = vector4(1153.0942, -471.8718, 66.5488,  53.4668), label = 'Mirror Park' },
['8']  = { coords = vector4( 242.2506,  223.9346,106.2868,  16.8834), label = 'Pac Bank' },
['9']  = { coords = vector4(-1082.5216, -249.0490, 37.7632, 102.9959), label = 'Life Invader' },
['10'] = { coords = vector4(-268.8057, -956.4702, 31.2231, 205.5015), label = 'Job Centre' },
['11'] = { coords = vector4(-956.4962,-3001.2795, 13.9451,  94.6072), label = 'Airport' },
['12'] = { coords = vector4( 118.6306,-1285.7673, 28.2717, 223.0086), label = 'VU' },
['13'] = { coords = vector4(-1033.8478,-1072.0928,  4.0830, 273.9618), label = 'Vespucci Canals' },
['14'] = { coords = vector4(  38.3441, -401.3483, 39.9219, 132.5527), label = 'Construction' },
['15'] = { coords = vector4( 176.5747,-1711.2000, 29.2918,  36.0097), label = 'Car Wash' },
['16'] = { coords = vector4( -1643.0443, 217.9225, 60.6411, 301.0437), label = 'University' }, 
['17'] = { coords = vector4( 1207.0387, -1505.4897, 34.6926, 95.3663), label = 'Fire Station' }, 
['18'] = { coords = vector4( -622.1088, -230.8255, 38.0519, 309.1125), label = 'Vangelico' }, 
['19'] = { coords = vector4( 288.5227, -1601.5027, 31.2657, 2.4907), label = 'Davis City Hall' }, 
}

-- Round duration (15 minutes)
Config.ClaimDuration = 15 * 60 * 1000

-- Circle radius (in meters)
Config.Radius = 400.0

-- Skull icon sprite
Config.SkullSprite = 310

-- 3D marker settings at claim location
Config.MarkerType  = 27
Config.MarkerSize  = { x = 1.4, y = 1.4, z = 1.0 }
Config.MarkerColor = { r = 255, g = 255, b =   0, a = 175 }