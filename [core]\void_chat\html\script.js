// html/script.js
window.addEventListener('message', function(event) {
  if (event.data.type === 'chatMessage') {
    const container = document.getElementById('chat-container');
    const msg = document.createElement('div');
    msg.className = 'chat-message';

    // author
    const authorSpan = document.createElement('span');
    const [r, g, b] = event.data.color;
    authorSpan.style.color = `rgb(${r},${g},${b})`;
    authorSpan.textContent = `${event.data.author}: `;

    // text
    const textSpan = document.createElement('span');
    textSpan.textContent = event.data.text;

    msg.appendChild(authorSpan);
    msg.appendChild(textSpan);
    container.appendChild(msg);

    // keep last 10
    if (container.children.length > 10) {
      container.removeChild(container.firstChild);
    }
  }
  if (event.data.type === 'openInput') {
    const inputBox = document.getElementById('chat-input');
    const input = document.getElementById('inputField');
    inputBox.classList.remove('hidden');
    input.focus();
  }
});

document.addEventListener('keydown', function(e) {
  if (e.key === 'Enter') {
    const input = document.getElementById('inputField');
    if (input.value.length > 0) {
      fetch(`https://${GetParentResourceName()}/chatResult`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json; charset=UTF-8' },
        body: JSON.stringify({ text: input.value })
      });
    }
    input.value = '';
    document.getElementById('chat-input').classList.add('hidden');
  }
  if (e.key === 'Escape') {
    document.getElementById('inputField').value = '';
    document.getElementById('chat-input').classList.add('hidden');
  }
});
