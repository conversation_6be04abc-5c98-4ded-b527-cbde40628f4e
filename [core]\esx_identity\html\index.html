<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Character Identity</title>
    <link rel="stylesheet" href="style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="hidden">
    <div class="identity-container">
        <div class="identity-card">
            <!-- Header -->
            <div class="card-header">
                <div class="logo-container">
                    <div class="logo-circle">
                        <span class="logo-icon">👤</span>
                    </div>
                </div>
                <h1 class="title">CHARACTER IDENTITY</h1>
                <p class="subtitle">Create your character's profile</p>
            </div>

            <!-- Form -->
            <div class="card-body">
                <form id="identity-form" class="form">
                    <!-- First Name -->
                    <div class="form-group">
                        <label for="firstname">First Name</label>
                        <input type="text" id="firstname" name="firstname" placeholder="Your character's first name" maxlength="50" required>
                        <div class="error" id="firstname-error"></div>
                    </div>

                    <!-- Last Name -->
                    <div class="form-group">
                        <label for="lastname">Last Name</label>
                        <input type="text" id="lastname" name="lastname" placeholder="Your character's last name" maxlength="50" required>
                        <div class="error" id="lastname-error"></div>
                    </div>

                    <!-- Date of Birth -->
                    <div class="form-group">
                        <label for="dob">Date Of Birth</label>
                        <input type="date" id="dob" name="dob" required>
                        <div class="error" id="dob-error"></div>
                    </div>

                    <!-- Height -->
                    <div class="form-group">
                        <label for="height">Height (CM)</label>
                        <input type="number" id="height" name="height" placeholder="Your character's height" required>
                        <div class="error" id="height-error"></div>
                    </div>

                    <!-- Gender -->
                    <div class="form-group">
                        <label>Gender</label>
                        <div class="gender-options">
                            <div class="gender-option">
                                <input type="radio" id="male" name="gender" value="m" required>
                                <label for="male" class="gender-label">
                                    <span class="gender-text">Male</span>
                                </label>
                            </div>
                            <div class="gender-option">
                                <input type="radio" id="female" name="gender" value="f" required>
                                <label for="female" class="gender-label">
                                    <span class="gender-text">Female</span>
                                </label>
                            </div>
                        </div>
                        <div class="error" id="gender-error"></div>
                    </div>

                    <!-- Buttons -->
                    <div class="form-actions">
                        <button type="button" class="btn btn-cancel" onclick="cancelForm()">Cancel</button>
                        <button type="submit" class="btn btn-confirm">Confirm</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
