local ESX = nil

-- Initialize ESX
TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)

-- Server callback to check player permissions
ESX.RegisterServerCallback('pedspawn:checkPermission', function(source, cb)
    local xPlayer = ESX.GetPlayerFromId(source)
    
    if xPlayer then
        local playerGroup = xPlayer.getGroup()
        
        -- Check if player has required permissions
        if playerGroup == 'admin' or playerGroup == 'cl' or playerGroup == 'owner' then
            cb(true)
        else
            cb(false)
        end
    else
        cb(false)
    end
end)