* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: transparent;
    overflow: hidden;
}

.hidden {
    display: none !important;
}

#garage-container {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60vw;
    height: 60vh;
    background: rgba(20, 20, 20, 0.95);
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
    border: 2px solid #555555;
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translate(-50%, -60%);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%);
    }
}

.garage-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    background: rgba(40, 40, 40, 0.8);
    border-radius: 15px 15px 0 0;
    border-bottom: 1px solid #555555;
}

.garage-header h2 {
    color: #ffffff;
    font-size: 24px;
    font-weight: bold;
}

#close-btn {
    background: #ff4757;
    color: white;
    border: none;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    font-size: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
}

#close-btn:hover {
    background: #ff3742;
    transform: scale(1.1);
}

.garage-content {
    padding: 25px;
    height: calc(100% - 140px);
    overflow-y: auto;
}

.vehicles-table {
    width: 100%;
    border-collapse: collapse;
    background: rgba(40, 40, 40, 0.6);
    border-radius: 10px;
    overflow: hidden;
}

.vehicles-table thead {
    background: rgba(60, 60, 60, 0.8);
}

.vehicles-table th {
    padding: 15px;
    text-align: left;
    color: #ffffff;
    font-weight: bold;
    font-size: 16px;
    border-bottom: 2px solid #555555;
}

.vehicles-table tbody tr {
    background: rgba(50, 50, 50, 0.6);
    transition: all 0.3s ease;
    cursor: pointer;
}

.vehicles-table tbody tr:hover {
    background: rgba(50, 150, 231, 0.8);
    transform: scale(1.02);
}

.vehicles-table tbody tr:nth-child(even) {
    background: rgba(45, 45, 45, 0.6);
}

.vehicles-table tbody tr:nth-child(even):hover {
    background: rgba(69, 140, 247, 0.8);
}

.vehicles-table td {
    padding: 15px;
    color: #ffffff;
    font-size: 14px;
    border-bottom: 1px solid rgba(85, 85, 85, 0.5);
}

.vehicle-name {
    font-weight: bold;
    color: #ffffff;
}

.vehicle-plate {
    color: #cccccc;
    font-family: 'Courier New', monospace;
}

.vehicle-type {
    color: #aaaaaa;
    text-transform: uppercase;
    font-size: 12px;
}

.vehicle-health {
    color: #4CAF50;
    font-weight: bold;
}

.garage-footer {
    padding: 15px 25px;
    background: rgba(30, 30, 30, 0.8);
    border-radius: 0 0 15px 15px;
    border-top: 1px solid #555555;
    text-align: center;
}

.garage-footer p {
    color: #cccccc;
    font-size: 14px;
}

/* Scrollbar styling */
.garage-content::-webkit-scrollbar {
    width: 8px;
}

.garage-content::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
}

.garage-content::-webkit-scrollbar-thumb {
    background: #666666;
    border-radius: 4px;
}

.garage-content::-webkit-scrollbar-thumb:hover {
    background: #4383e4;
}