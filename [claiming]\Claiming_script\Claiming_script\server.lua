-- server.lua
ESX = nil
TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)

----------------------------------------------------------------
-- SERVER-SIDE WEAPON ENFORCEMENT FOR DEAGLE ONLY MODES
----------------------------------------------------------------
local weaponEnforcementActive = false

-- List of allowed weapons during deagle only modes
local allowedWeapons = {
  ['weapon_pistol50'] = true,  -- Deagle
  ['weapon_unarmed'] = true    -- Fists
}

-- Function to check if a weapon is allowed
local function IsWeaponAllowed(weaponName)
  return allowedWeapons[weaponName:lower()] == true
end

-- Server-side weapon enforcement thread
local function WeaponEnforcementThread()
  Citizen.CreateThread(function()
    while weaponEnforcementActive do
      Citizen.Wait(2000) -- Check every 2 seconds

      local players = GetPlayers()
      for _, playerId in ipairs(players) do
        local xPlayer = ESX.GetPlayerFromId(playerId)
        if xPlayer then
          local loadout = xPlayer.getLoadout()

          -- Check each weapon in player's loadout
          for _, weapon in ipairs(loadout) do
            if not IsWeaponAllowed(weapon.name) then
              -- Remove the weapon
              xPlayer.removeWeapon(weapon.name)
              print('[CLAIMING SERVER] Removed weapon ' .. weapon.name .. ' from player ' .. playerId)

              -- Give deagle if they don't have it
              if not xPlayer.hasWeapon('weapon_pistol50') then
                xPlayer.addWeapon('weapon_pistol50', 250)
                print('[CLAIMING SERVER] Gave deagle to player ' .. playerId)
              end
            end
          end
        end
      end
    end
  end)
end

-- Start weapon enforcement
function StartWeaponEnforcement()
  if not weaponEnforcementActive then
    weaponEnforcementActive = true
    WeaponEnforcementThread()
    print('[CLAIMING SERVER] Started weapon enforcement')
  end
end

-- Stop weapon enforcement
function StopWeaponEnforcement()
  weaponEnforcementActive = false
  print('[CLAIMING SERVER] Stopped weapon enforcement')
end

local claimActive = false
local lastWinnerJobLabel = nil
local currentMode = 'any' -- Track current game mode for claim logging
local currentRoundData = nil -- Store current round data for new players
local claimedByJob = nil -- Track which job claimed the current location
local carWarfareEnabled = false -- Track car warfare state
local siphonEnabled = false -- Track siphon state
local heliModeEnabled = false -- Track standalone heli mode state

-- Startup message
Citizen.CreateThread(function()
  Citizen.Wait(1000)
  print('[CLAIMING] Car warfare system initialized - Default state: DISABLED')
  print('[CLAIMING] Car warfare can be toggled via claiming UI button')
end)
local roundEnding = false -- Flag to prevent multiple end messages

-- Kill tracking for claiming rounds
local roundKills = {} -- Track kills per player during active rounds
local roundActive = false -- Track if round is active for kill counting

-- Function to reset kill tracking
local function resetKillTracking()
    roundKills = {}
    roundActive = false
end

-- Function to start kill tracking
local function startKillTracking()
    roundKills = {}
    roundActive = true
    print('[CLAIMING] Kill tracking started for new round')
end

-- Function to add kill to player's count
local function addKillToPlayer(playerId)
    if not roundActive then return end

    local playerName = GetPlayerName(playerId)
    if not playerName then return end

    if not roundKills[playerId] then
        roundKills[playerId] = {
            name = playerName,
            kills = 0
        }
    end

    roundKills[playerId].kills = roundKills[playerId].kills + 1
    print('[CLAIMING] ' .. playerName .. ' now has ' .. roundKills[playerId].kills .. ' kills this round')
end

-- Function to get top killer
local function getTopKiller()
    local topKiller = nil
    local maxKills = 0

    for playerId, data in pairs(roundKills) do
        if data.kills > maxKills then
            maxKills = data.kills
            topKiller = data
        end
    end

    return topKiller, maxKills
end

-- ═══════════════════════════════════════════════════════════════════════════════
-- INTEGRATED DEAGLE ONLY SYSTEM (SERVER-SIDE)
-- ═══════════════════════════════════════════════════════════════════════════════

local deagleEnabled = false
local allowedGroups = { 'admin', 'cl', 'owner' }

local function contains(tbl, val)
    for _,v in ipairs(tbl) do
        if v == val then return true end
    end
    return false
end

-- ═══════════════════════════════════════════════════════════════════════════════
-- DEAGLE ONLY SYSTEM - CLAIMING UI EXCLUSIVE CONTROL
-- ═══════════════════════════════════════════════════════════════════════════════
-- Deagle only mode can ONLY be controlled through the claiming UI:
-- - Turn ON: Start a claiming round with "Deagle Only" mode
-- - Turn OFF: End the current round OR start a different round mode
-- No standalone commands available - claiming UI exclusive control
-- ═══════════════════════════════════════════════════════════════════════════════

-- Debug command to check deagle status (admin only)
RegisterCommand('deaglestatus', function(source, args)
    local src = source
    if src == 0 then
        print('[CLAIMING] Deagle Only Status: ' .. (deagleEnabled and 'ENABLED' or 'DISABLED'))
        print('[CLAIMING] Current Mode: ' .. currentMode)
        print('[CLAIMING] Claim Active: ' .. (claimActive and 'YES' or 'NO'))
        return
    end

    local xPlayer = ESX.GetPlayerFromId(src)
    if not xPlayer then return end
    local grp = xPlayer.getGroup()

    if not contains(allowedGroups, grp) then
        TriggerClientEvent('chat:addMessage', src, {
            color = {255,0,0},
            args = {"^*SYSTEM^r", "YOU DON'T HAVE PERMISSION."}
        })
        return
    end

    TriggerClientEvent('chat:addMessage', src, {
        color = {0,255,255},
        args = {"^*DEAGLE STATUS^r", "Deagle Only: " .. (deagleEnabled and "ENABLED" or "DISABLED") .. " | Mode: " .. currentMode .. " | Active: " .. (claimActive and "YES" or "NO")}
    })
end, false)

-- Car Warfare Toggle - Fixed Event Registration
print('[CLAIMING] Registering car warfare toggle event: void_claiming:toggleCarWarfare')
RegisterNetEvent('void_claiming:toggleCarWarfare')
AddEventHandler('void_claiming:toggleCarWarfare', function()
  local src = source
  print('[CLAIMING] ===== CAR WARFARE EVENT RECEIVED =====')
  print('[CLAIMING] Source: ' .. tostring(src))
  print('[CLAIMING] Player Name: ' .. GetPlayerName(src))
  print('[CLAIMING] Current carWarfareEnabled: ' .. tostring(carWarfareEnabled))

  -- Check if hasPermission function exists and works
  local hasPerms = hasPermission(src)
  print('[CLAIMING] hasPermission result: ' .. tostring(hasPerms))

  if not hasPerms then
    print('[CLAIMING] Player ' .. src .. ' does not have permission for car warfare toggle')
    TriggerClientEvent('chat:addMessage', src, {
      template = '<div><b style="color:#c1a2fc">CLAIMING</b> <span style="color:#ff0000">No permission for car warfare toggle</span></div>'
    })
    return
  end

  carWarfareEnabled = not carWarfareEnabled
  print('[CLAIMING] Car warfare toggled to: ' .. tostring(carWarfareEnabled))

  if carWarfareEnabled then
    -- Send enabled message in green
    TriggerClientEvent('chat:addMessage', -1, {
      template = '<div><b style="color:#c1a2fc">CLAIMING</b> <span style="color:#97f57d">Car Warfare enabled!</span></div>'
    })
  else
    -- Send disabled message in red
    TriggerClientEvent('chat:addMessage', -1, {
      template = '<div><b style="color:#c1a2fc">CLAIMING</b> <span style="color:#ff0000">Car Warfare disabled!</span></div>'
    })
  end

  -- Update all clients with the new state
  print('[CLAIMING] Sending setCarWarfare event to all clients with state: ' .. tostring(carWarfareEnabled))
  TriggerClientEvent('claiming:setCarWarfare', -1, carWarfareEnabled)

  print('[CLAIMING] Car warfare ' .. (carWarfareEnabled and 'enabled' or 'disabled') .. ' by ' .. GetPlayerName(src))
  print('[CLAIMING] ===== CAR WARFARE EVENT COMPLETE =====')
end)

-- Test commands removed - car warfare only controlled by UI button

-- Debug command to check kill stats during round (admin only)
RegisterCommand('killstats', function(source, args)
    local src = source
    if src == 0 then
        -- Server console
        if roundActive then
            print('[CLAIMING] Current round kill stats:')
            for playerId, data in pairs(roundKills) do
                print('  ' .. data.name .. ': ' .. data.kills .. ' kills')
            end
            local topKiller, maxKills = getTopKiller()
            if topKiller then
                print('[CLAIMING] Current top killer: ' .. topKiller.name .. ' with ' .. maxKills .. ' kills')
            else
                print('[CLAIMING] No kills recorded yet this round')
            end
        else
            print('[CLAIMING] No active round for kill tracking')
        end
        return
    end

    local xPlayer = ESX.GetPlayerFromId(src)
    if not xPlayer then return end
    local grp = xPlayer.getGroup()

    if not contains(allowedGroups, grp) then
        TriggerClientEvent('chat:addMessage', src, {
            color = {255,0,0},
            args = {"^*SYSTEM^r", "YOU DON'T HAVE PERMISSION."}
        })
        return
    end

    if roundActive then
        TriggerClientEvent('chat:addMessage', src, {
            color = {0,255,255},
            args = {"^*KILL STATS^r", "Round active - tracking " .. #roundKills .. " players with kills"}
        })

        local topKiller, maxKills = getTopKiller()
        if topKiller then
            TriggerClientEvent('chat:addMessage', src, {
                color = {255,255,0},
                args = {"^*TOP KILLER^r", topKiller.name .. " leads with " .. maxKills .. " kills"}
            })
        end
    else
        TriggerClientEvent('chat:addMessage', src, {
            color = {255,255,0},
            args = {"^*KILL STATS^r", "No active round"}
        })
    end
end, false)

-- Kill detection and healing system for deagle mode + kill tracking for claiming rounds + siphon
RegisterNetEvent('esx:onPlayerDeath')
AddEventHandler('esx:onPlayerDeath', function(data)
    local victim = source -- The player who died
    local killer = data.killerServerId -- The server ID of the killer

    print('[DEATH DEBUG] Victim: ' .. tostring(victim) .. ', Killer: ' .. tostring(killer) .. ', KilledByPlayer: ' .. tostring(data.killedByPlayer))

    -- Only process if killed by another player
    if not data.killedByPlayer or not killer or killer == victim then
        return
    end

    -- Track kills during claiming rounds
    if roundActive then
        addKillToPlayer(killer)
    end

    -- Heal killer if deagle mode is enabled OR siphon is enabled
    local shouldHeal = false
    local healReason = ""

    if deagleEnabled then
        shouldHeal = true
        healReason = "DEAGLE"
    elseif siphonEnabled then
        shouldHeal = true
        healReason = "SIPHON"
    end

    if shouldHeal then
        -- Heal the killer to max health
        if healReason == "SIPHON" then
            TriggerClientEvent('siphon:healPlayer', killer)
        else
            TriggerClientEvent('deagle:healPlayer', killer, true) -- true = silent mode for deagle
        end
        print('[' .. healReason .. '] Player ' .. killer .. ' healed for killing player ' .. victim)
    end
end)

-- Alternative method: Custom kill detection
RegisterNetEvent('deagle:playerKilled')
AddEventHandler('deagle:playerKilled', function(killerId, victimId)
    -- Track kills during claiming rounds
    if roundActive and killerId and victimId and killerId ~= victimId then
        addKillToPlayer(killerId)
    end

    -- Heal killer if deagle mode is enabled OR siphon is enabled
    if killerId and victimId and killerId ~= victimId then
        local shouldHeal = false
        local healReason = ""

        if deagleEnabled then
            shouldHeal = true
            healReason = "DEAGLE"
        elseif siphonEnabled then
            shouldHeal = true
            healReason = "SIPHON"
        end

        if shouldHeal then
            -- Heal the killer to max health
            if healReason == "SIPHON" then
                TriggerClientEvent('siphon:healPlayer', killerId)
            else
                TriggerClientEvent('deagle:healPlayer', killerId, true) -- true = silent mode for deagle
            end
            print('[' .. healReason .. '] Player ' .. killerId .. ' healed for killing player ' .. victimId)
        end
    end
end)

-- Backup method: baseevents kill detection
AddEventHandler('baseevents:onPlayerKilled', function(killerId, data)
    local victim = source

    print('[BASEEVENTS DEBUG] Victim: ' .. tostring(victim) .. ', Killer: ' .. tostring(killerId))

    if not killerId or killerId == victim then
        return
    end

    -- Track kills during claiming rounds
    if roundActive then
        addKillToPlayer(killerId)
    end

    -- Heal killer if deagle mode is enabled OR siphon is enabled
    local shouldHeal = false
    local healReason = ""

    if deagleEnabled then
        shouldHeal = true
        healReason = "DEAGLE"
    elseif siphonEnabled then
        shouldHeal = true
        healReason = "SIPHON"
    end

    if shouldHeal then
        -- Heal the killer to max health
        if healReason == "SIPHON" then
            TriggerClientEvent('siphon:healPlayer', killerId)
        else
            TriggerClientEvent('deagle:healPlayer', killerId, true) -- true = silent mode for deagle
        end
        print('[' .. healReason .. ' BASEEVENTS] Player ' .. killerId .. ' healed for killing player ' .. victim)
    end
end)

-- Simple kill detection using player health monitoring (DISABLED to prevent multiple heals)
-- This system was causing multiple players to get healed for the same kill
-- The primary kill detection systems (esx:onPlayerDeath and deagle:playerKilled) are sufficient
local playerHealths = {}

--[[
DISABLED: This backup kill detection system was causing issues where multiple players
could get healed for the same kill. The primary systems above handle kill detection properly.

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(1000) -- Check every second

        if deagleEnabled then
            local players = GetPlayers()

            for _, playerId in ipairs(players) do
                local playerPed = GetPlayerPed(playerId)
                local currentHealth = GetEntityHealth(playerPed)
                local previousHealth = playerHealths[playerId] or currentHealth

                -- If player died (health went to 0 or below)
                if previousHealth > 0 and currentHealth <= 0 then
                    -- Find potential killer (player who recently shot)
                    for _, potentialKiller in ipairs(players) do
                        if potentialKiller ~= playerId then
                            local killerPed = GetPlayerPed(potentialKiller)
                            local weapon = GetSelectedPedWeapon(killerPed)
                            local deagleHash = GetHashKey("weapon_pistol50")

                            -- If they have deagle equipped, they likely got the kill
                            if weapon == deagleHash then
                                -- Heal the killer to max health (silent for x2 deagle and deagleonly modes)
                                TriggerClientEvent('deagle:healPlayer', potentialKiller, true) -- true = silent mode

                                print('[DEAGLE] Player ' .. potentialKiller .. ' healed for killing player ' .. playerId)
                                break
                            end
                        end
                    end
                end

                playerHealths[playerId] = currentHealth
            end
        else
            Citizen.Wait(5000) -- Wait longer when deagle mode is disabled
        end
    end
end)
--]]

-- ═══════════════════════════════════════════════════════════════════════════════
-- CLAIMING SYSTEM
-- ═══════════════════════════════════════════════════════════════════════════════

local function hasPermission(src)
  if src == 0 then return true end
  local xP = ESX.GetPlayerFromId(src)
  local grp = xP and xP.getGroup() or ''
  return (grp == 'admin' or grp == 'cl' or grp == 'owner')
end

RegisterCommand('claiming', function(source, args)
  if hasPermission(source) then
    TriggerClientEvent('void_claiming:openUI', source)
  else
    TriggerClientEvent('chat:addMessage', source, {
      template = '<div><b style="color:#c1a2fc">CLAIMING</b> <span style="color:white">Insufficient permissions.</span></div>'
    })
  end
end, false)

-- Siphon Command (Staff Only)
RegisterCommand('siphon', function(source, args)
  local src = source
  if not hasPermission(src) then
    TriggerClientEvent('chat:addMessage', src, {
      template = '<div><b style="color:#c1a2fc">CLAIMING</b> <span style="color:#ff0000">No permission for siphon command</span></div>'
    })
    return
  end

  siphonEnabled = not siphonEnabled

  if siphonEnabled then
    -- Send enabled message in green
    TriggerClientEvent('chat:addMessage', -1, {
      template = '<div><b style="color:#c1a2fc">CLAIMING</b> <span style="color:#97f57d">Siphon enabled! Players will heal on kill.</span></div>'
    })
  else
    -- Send disabled message in red
    TriggerClientEvent('chat:addMessage', -1, {
      template = '<div><b style="color:#c1a2fc">CLAIMING</b> <span style="color:#ff0000">Siphon disabled!</span></div>'
    })
  end

  -- Update all clients with the new state
  TriggerClientEvent('claiming:setSiphon', -1, siphonEnabled)

  print('[CLAIMING] Siphon ' .. (siphonEnabled and 'enabled' or 'disabled') .. ' by ' .. GetPlayerName(src))
end, false)

-- Heli Mode Command (Staff Only) - Independent of claiming rounds
RegisterCommand('heli', function(source, args)
  local src = source
  if not hasPermission(src) then
    TriggerClientEvent('chat:addMessage', src, {
      template = '<div><b style="color:#c1a2fc">CLAIMING</b> <span style="color:#ff0000">No permission for heli command</span></div>'
    })
    return
  end

  heliModeEnabled = not heliModeEnabled

  if heliModeEnabled then
    -- Send enabled message in blue
    TriggerClientEvent('chat:addMessage', -1, {
      template = '<div><b style="color:#c1a2fc">CLAIMING</b> <span style="color:#4fc3f7">Heli mode enabled! Players can spawn helicopters from gang garages.</span></div>'
    })
  else
    -- Send disabled message in red
    TriggerClientEvent('chat:addMessage', -1, {
      template = '<div><b style="color:#c1a2fc">CLAIMING</b> <span style="color:#ff0000">Heli mode disabled!</span></div>'
    })
  end

  -- Update all clients with the new state (gang_heli_garage system)
  TriggerClientEvent('gang_heli_garage:roundStatus', -1, heliModeEnabled)

  -- Update claiming script clients with the new state
  TriggerClientEvent('claiming:setHeliMode', -1, heliModeEnabled)

  print('[CLAIMING] Heli mode ' .. (heliModeEnabled and 'enabled' or 'disabled') .. ' by ' .. GetPlayerName(src))
end, false)

-- Start Round\67
RegisterNetEvent('void_claiming:startRound')
AddEventHandler('void_claiming:startRound', function(locKey, mode)
  local src = source
  if not hasPermission(src) then return end
  if claimActive then
    return TriggerClientEvent('chat:addMessage', src, {
      template = '<div><b style="color:#c1a2fc">CLAIMING</b> <span style="color:white">A round is already in progress.</span></div>'
    })
  end

  -- Ensure we're not in a roundEnding state from previous round
  roundEnding = false
  claimActive = true
  lastWinnerJobLabel = nil
  claimedByJob = nil -- Reset claimed job for new round
  currentMode = mode -- Store current mode for claim logging
  roundEnding = false -- Reset round ending flag

  -- Initialize kill tracking for this round
  startKillTracking()
  local loc = Config.Locations[locKey]
  if not loc then
    claimActive = false
    return
  end

  -- Store current round data for new players
  currentRoundData = {
    locKey = locKey,
    coords = loc.coords,
    mode = mode,
    claimedByJob = nil
  }
  -- Store start time for timer sync
  currentRoundData.startTime = GetGameTimer()
  -- Enhanced game mode setup
  TriggerClientEvent('claiming:mode', -1, mode)
  TriggerClientEvent('claiming:start', -1, locKey, loc.coords, mode)

  -- Activate specific game modes
  if mode == 'deagleonly' then
    -- Enable integrated deagle mode
    deagleEnabled = true
    TriggerClientEvent('deagle:setMode', -1, true)
    -- Give all players deagles after a short delay
    Citizen.SetTimeout(2000, function()
      for _, playerId in ipairs(GetPlayers()) do
        TriggerClientEvent('claiming:giveDeagle', playerId)
      end
    end)
    print('[CLAIMING] Deagle only mode enabled for claiming round')
    -- Disable helicopter mode when starting deagle mode (overrides standalone heli mode)
    TriggerClientEvent('gang_heli_garage:roundStatus', -1, false)

    -- Start server-side weapon enforcement
    StartWeaponEnforcement()
  elseif mode == 'x2headdy' then
    -- Enable x2 headdy mode (deagle only with 99 headshot damage, body shots do no damage - headshot-only mode)
    deagleEnabled = true
    TriggerClientEvent('deagle:setMode', -1, true)
    TriggerClientEvent('x2headdy:setMode', -1, true)
    -- Give all players deagles after a short delay
    Citizen.SetTimeout(2000, function()
      for _, playerId in ipairs(GetPlayers()) do
        TriggerClientEvent('claiming:giveDeagle', playerId)
      end
    end)
    print('[CLAIMING] x2 headdy mode enabled for claiming round (headshot-only)')
    -- Disable helicopter mode when starting x2 headdy mode (overrides standalone heli mode)
    TriggerClientEvent('gang_heli_garage:roundStatus', -1, false)

    -- Start server-side weapon enforcement
    StartWeaponEnforcement()
  else
    -- Any mode - ensure all restrictions are disabled
    deagleEnabled = false
    TriggerClientEvent('deagle:setMode', -1, false)
    TriggerClientEvent('x2headdy:setMode', -1, false)

    -- Stop server-side weapon enforcement
    StopWeaponEnforcement()

    -- Preserve standalone heli mode if it was enabled independently
    if heliModeEnabled then
      TriggerClientEvent('gang_heli_garage:roundStatus', -1, true)
      print('[CLAIMING] Deagle only mode disabled - any weapons round started (standalone heli mode preserved)')
    else
      TriggerClientEvent('gang_heli_garage:roundStatus', -1, false)
      print('[CLAIMING] Deagle only mode disabled - any weapons round started')
    end
  end

  -- Send round start message
  TriggerClientEvent('chat:addMessage', -1, {
    template = '<div><b style="color:#c1a2fc">CLAIMING</b> <span style="color:white">Started at '..loc.label..' ('..mode..')</span></div>'
  })

  TriggerClientEvent('void_claiming:newLog', -1,
    GetPlayerName(src),
    ESX.GetPlayerFromId(src):getGroup(),
    'Start',
    mode,
    loc.label
  )
end)

-- End Round
RegisterNetEvent('void_claiming:endRound')
AddEventHandler('void_claiming:endRound', function()
  local src = source
  if not hasPermission(src) then return end

  -- Prevent multiple end messages
  if roundEnding then return end
  roundEnding = true

  -- Get top killer data BEFORE stopping kill tracking
  local topKiller, maxKills = getTopKiller()

  TriggerClientEvent('claiming:end', -1)
  TriggerClientEvent('claiming:clearBlips', -1)
  -- Disable integrated deagle mode and x2headdy mode
  deagleEnabled = false
  TriggerClientEvent('deagle:setMode', -1, false)
  TriggerClientEvent('x2headdy:setMode', -1, false)

  -- Always disable heli mode when round ends (auto-disable feature)
  heliModeEnabled = false
  TriggerClientEvent('gang_heli_garage:roundStatus', -1, false)
  TriggerClientEvent('claiming:setHeliMode', -1, false)
  print('[CLAIMING] All game modes disabled - round ended (heli mode auto-disabled)')

  -- Send single end message with winner info
  if lastWinnerJobLabel then
    TriggerClientEvent('chat:addMessage', -1, {
      template = '<div><b style="color:#c1a2fc">CLAIMING</b> <span style="color:white">Ended - '..lastWinnerJobLabel..' won the round</span></div>'
    })
  else
    TriggerClientEvent('chat:addMessage', -1, {
      template = '<div><b style="color:#c1a2fc">CLAIMING</b> <span style="color:white">Ended</span></div>'
    })
  end

  -- Display top killer if there were any kills
  if topKiller and maxKills > 0 then
    TriggerClientEvent('chat:addMessage', -1, {
      template = '<div><b style="color:#c1a2fc">TOP KILLER</b> <span style="color:white">' .. topKiller.name .. ' got the most kills this round with ' .. maxKills .. ' kills!</span></div>'
    })
    print('[CLAIMING] Top killer: ' .. topKiller.name .. ' with ' .. maxKills .. ' kills')
  end

  -- Stop kill tracking AFTER displaying results
  roundActive = false

  -- Kill all players when round ends
  TriggerClientEvent('claiming:killAllPlayers', -1)

  TriggerClientEvent('void_claiming:newLog', -1,
    GetPlayerName(src),
    ESX.GetPlayerFromId(src):getGroup(),
    'End',
    '',
    ''
  )
  lastWinnerJobLabel = nil
  claimActive = false
  currentMode = 'any' -- Reset mode when round ends

  -- Notify all clients that mode has been reset to 'any'
  TriggerClientEvent('claiming:mode', -1, 'any')
  currentRoundData = nil -- Clear round data
  claimedByJob = nil -- Clear claimed job
  roundEnding = false -- Reset the flag

  -- Reset kill tracking
  resetKillTracking()
  print('[CLAIMING] Kill tracking reset')
end)

-- Pause/Resume Round (toggle functionality)
RegisterNetEvent('void_claiming:pauseRound')
AddEventHandler('void_claiming:pauseRound', function()
  local src = source
  if not hasPermission(src) then return end
  TriggerClientEvent('claiming:pause', -1)
  TriggerClientEvent('chat:addMessage', -1, {
    template = '<div><b style="color:#c1a2fc">CLAIMING</b> <span style="color:white">Round Paused/Resumed</span></div>'
  })
  TriggerClientEvent('void_claiming:newLog', -1,
    GetPlayerName(src),
    ESX.GetPlayerFromId(src):getGroup(),
    'Pause',
    '',
    ''
  )
end)

-- Resume Round
RegisterNetEvent('void_claiming:resumeRound')
AddEventHandler('void_claiming:resumeRound', function()
  local src = source
  if not hasPermission(src) then return end
  TriggerClientEvent('claiming:resume', -1)
  TriggerClientEvent('chat:addMessage', -1, {
    template = '<div><b style="color:#c1a2fc">CLAIMING</b> <span style="color:white">Resumed</span></div>'
  })
  TriggerClientEvent('void_claiming:newLog', -1,
    GetPlayerName(src),
    ESX.GetPlayerFromId(src):getGroup(),
    'Resume',
    '',
    ''
  )
end)

-- Player Claim Attempt
RegisterNetEvent('claiming:attempt')
AddEventHandler('claiming:attempt', function(locKey)
  local src = source
  if not claimActive then
    TriggerClientEvent('chat:addMessage', src, {
      template = '<div><b style="color:#c1a2fc">CLAIMING</b> <span style="color:white">No active claiming round.</span></div>'
    })
    return
  end

  local xP = ESX.GetPlayerFromId(src)
  if not xP then return end

  local jobName = xP.job and xP.job.name or 'unemployed'
  local jobLabel = xP.job and (xP.job.label or xP.job.name) or 'Unknown'

  -- Check if the same job already claimed this location
  if claimedByJob == jobName then
    TriggerClientEvent('chat:addMessage', src, {
      template = '<div><b style="color:#c1a2fc">CLAIMING</b> <span style="color:white">Your job already claimed this location.</span></div>'
    })
    return
  end

  -- Claim successful
  claimedByJob = jobName
  lastWinnerJobLabel = jobLabel

  -- Update round data
  if currentRoundData then
    currentRoundData.claimedByJob = jobName
  end

  -- Notify all clients about the claim (with job info)
  TriggerClientEvent('claiming:claimed', -1, locKey, jobName, jobLabel)
  TriggerClientEvent('chat:addMessage', -1, {
    template = '<div><b style="color:#c1a2fc">CLAIMING</b> <span style="color:white">' .. jobLabel ..
               ' claimed ' .. Config.Locations[locKey].label .. '</span></div>'
  })

  print('[CLAIMING] Location ' .. locKey .. ' claimed by job: ' .. jobName .. ' (' .. jobLabel .. ')')
  -- Don't log claims to activity logs as requested
end)

-- Sync new players with current round state
RegisterNetEvent('claiming:requestSync')
AddEventHandler('claiming:requestSync', function()
  local src = source

  if claimActive and currentRoundData then
    print('[CLAIMING] Syncing new player ' .. src .. ' with active round')

    -- Send round start data first
    TriggerClientEvent('claiming:start', src, currentRoundData.locKey, currentRoundData.coords, currentRoundData.mode)

    -- Send game mode data
    TriggerClientEvent('claiming:mode', src, currentRoundData.mode)

    -- Sync timer with server time (with delay to ensure round start is processed first)
    if currentRoundData.startTime then
      Citizen.SetTimeout(100, function()
        local now = GetGameTimer()
        local timeLeft = Config.ClaimDuration - (now - currentRoundData.startTime)

        -- safety: prevent negative
        if timeLeft < 0 then timeLeft = 0 end

        TriggerClientEvent("claiming:setTimer", src, timeLeft)
        print('[CLAIMING] Synced timer for player ' .. src .. ' with ' .. math.floor(timeLeft / 1000) .. ' seconds left')
      end)
    end

    -- If location is claimed, send claim data
    if currentRoundData.claimedByJob then
      TriggerClientEvent('claiming:claimed', src, currentRoundData.locKey, currentRoundData.claimedByJob, lastWinnerJobLabel)
    end

    -- Send deagle mode if active
    if currentRoundData.mode == 'deagleonly' and deagleEnabled then
      TriggerClientEvent('deagle:setMode', src, true)
      -- Give deagle after short delay
      Citizen.SetTimeout(1000, function()
        TriggerClientEvent('claiming:giveDeagle', src)
      end)
    elseif currentRoundData.mode == 'x2headdy' and deagleEnabled then
      TriggerClientEvent('deagle:setMode', src, true)
      TriggerClientEvent('x2headdy:setMode', src, true)
      -- Give deagle after short delay
      Citizen.SetTimeout(1000, function()
        TriggerClientEvent('claiming:giveDeagle', src)
      end)
    end

    -- Send car warfare state
    TriggerClientEvent('claiming:setCarWarfare', src, carWarfareEnabled)

    -- Send siphon state
    TriggerClientEvent('claiming:setSiphon', src, siphonEnabled)

    -- Send standalone heli mode state (independent of round mode)
    TriggerClientEvent('claiming:setHeliMode', src, heliModeEnabled)
    TriggerClientEvent('gang_heli_garage:roundStatus', src, heliModeEnabled)
  end
end)

-- Auto-sync when player spawns
AddEventHandler('esx:playerLoaded', function(playerId, xPlayer)
  if claimActive and currentRoundData then
    -- Small delay to ensure player is fully loaded
    Citizen.SetTimeout(2000, function()
      print('[CLAIMING] Auto-syncing player ' .. playerId .. ' with active round')
      -- Manually trigger sync for the specific player
      local src = playerId

      -- Send round start data
      TriggerClientEvent('claiming:start', src, currentRoundData.locKey, currentRoundData.coords, currentRoundData.mode)

      -- Send game mode data
      TriggerClientEvent('claiming:mode', src, currentRoundData.mode)

      -- Sync timer with server time (with delay to ensure round start is processed first)
      if currentRoundData.startTime then
        Citizen.SetTimeout(100, function()
          local now = GetGameTimer()
          local timeLeft = Config.ClaimDuration - (now - currentRoundData.startTime)

          -- safety: prevent negative
          if timeLeft < 0 then timeLeft = 0 end

          TriggerClientEvent("claiming:setTimer", src, timeLeft)
          print('[CLAIMING] Auto-synced timer for player ' .. src .. ' with ' .. math.floor(timeLeft / 1000) .. ' seconds left')
        end)
      end

      -- If location is claimed, send claim data
      if currentRoundData.claimedByJob then
        TriggerClientEvent('claiming:claimed', src, currentRoundData.locKey, currentRoundData.claimedByJob, lastWinnerJobLabel)
      end

      -- Send deagle mode if active
      if currentRoundData.mode == 'deagleonly' and deagleEnabled then
        TriggerClientEvent('deagle:setMode', src, true)
        -- Give deagle after short delay
        Citizen.SetTimeout(1000, function()
          TriggerClientEvent('claiming:giveDeagle', src)
        end)
      elseif currentRoundData.mode == 'x2headdy' and deagleEnabled then
        TriggerClientEvent('deagle:setMode', src, true)
        TriggerClientEvent('x2headdy:setMode', src, true)
        -- Give deagle after short delay
        Citizen.SetTimeout(1000, function()
          TriggerClientEvent('claiming:giveDeagle', src)
        end)
      end

      -- Send car warfare state
      TriggerClientEvent('claiming:setCarWarfare', src, carWarfareEnabled)

      -- Send siphon state
      TriggerClientEvent('claiming:setSiphon', src, siphonEnabled)

      -- Send standalone heli mode state (independent of round mode)
      TriggerClientEvent('claiming:setHeliMode', src, heliModeEnabled)
      TriggerClientEvent('gang_heli_garage:roundStatus', src, heliModeEnabled)
    end)
  end
end)

-- Additional sync triggers for different connection scenarios
AddEventHandler('playerJoining', function()
  local src = source
  if claimActive and currentRoundData then
    -- Delay to ensure player is fully connected
    Citizen.SetTimeout(3000, function()
      print('[CLAIMING] Player ' .. src .. ' joining during active round - triggering sync')
      TriggerEvent('claiming:requestSync', src)
    end)
  end
end)

-- Sync for players who connect during active rounds
RegisterNetEvent('claiming:playerConnected')
AddEventHandler('claiming:playerConnected', function()
  local src = source
  if claimActive and currentRoundData then
    print('[CLAIMING] Player ' .. src .. ' connected during active round - triggering sync')
    TriggerEvent('claiming:requestSync', src)
  end
end)


