// html/script.js

// hide/show the UI on NUI message
window.addEventListener('message', ev => {
  if (ev.data.action === 'openUI') {
    document.getElementById('voidClaimingUI').style.display = 'block';
  }
});

function closeUI() {
  document.getElementById('voidClaimingUI').style.display = 'none';
  fetch(`https://${GetParentResourceName()}/closeUI`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json; charset=UTF-8' },
    body: JSON.stringify({})
  });
}
