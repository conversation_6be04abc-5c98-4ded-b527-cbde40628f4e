(function() {
    let characters = [];
    let selectedCharacter = null;

    const CharacterOverlay = {};

    CharacterOverlay.Show = function(charactersData) {
        console.log('[MULTICHAR] Showing overlay with characters:', charactersData);

        characters = charactersData || [];

        // Show overlay
        $("body").css({ display: "block" });
        $(".character-overlay").css({ display: "flex" });

        // Render character list
        CharacterOverlay.RenderCharacterList();

        // Select first character by default
        if (characters && Object.keys(characters).length > 0) {
            const firstCharId = Object.keys(characters)[0];
            CharacterOverlay.SelectCharacter(characters[firstCharId]);
        }
    };

    CharacterOverlay.Hide = function() {
        $("body").css({ display: "none" });
        $(".character-overlay").css({ display: "none" });
    };

    CharacterOverlay.RenderCharacterList = function() {
        const characterList = $("#character-list");
        characterList.empty();

        // Add characters to list (no create new character option)
        for (const charId in characters) {
            const character = characters[charId];
            if (character) {
                const characterHtml = `
                    <div class="character-item" data-charid="${character.id}">
                        <div class="character-name">${character.firstname} ${character.lastname}</div>
                        <div class="character-job">${character.job}</div>
                    </div>
                `;
                characterList.append(characterHtml);
            }
        }

        // Add click handlers
        $(".character-item").click(function() {
            const charId = parseInt($(this).data("charid"));
            const character = characters[charId];
            if (character) {
                CharacterOverlay.SelectCharacter(character);
            }
        });
    };

    CharacterOverlay.SelectCharacter = function(character) {
        selectedCharacter = character;

        // Update selection visual
        $(".character-item").removeClass("selected");
        $(`.character-item[data-charid="${character.id}"]`).addClass("selected");

        // Update character info
        CharacterOverlay.DisplayCharacterInfo(character);

        // Show select button
        $("#select-btn").show();

        // Notify game to show this character model
        $.post(`https://${GetParentResourceName()}/previewCharacter`, JSON.stringify({
            charid: character.id
        }));
    };

    CharacterOverlay.DisplayCharacterInfo = function(character) {
        const infoContainer = $("#character-info");

        if (!character) {
            infoContainer.html('<div class="no-selection">Select a character to view details</div>');
            return;
        }

        const infoHtml = `
            <div class="info-row">
                <span class="info-label">Name:</span>
                <span class="info-value">${character.firstname} ${character.lastname}</span>
            </div>
            <div class="info-row">
                <span class="info-label">Job:</span>
                <span class="info-value">${character.job}</span>
            </div>
            <div class="info-row">
                <span class="info-label">Money:</span>
                <span class="info-value">$${character.money}</span>
            </div>
            <div class="info-row">
                <span class="info-label">Bank:</span>
                <span class="info-value">$${character.bank}</span>
            </div>
            <div class="info-row">
                <span class="info-label">Date of Birth:</span>
                <span class="info-value">${character.dateofbirth}</span>
            </div>
            <div class="info-row">
                <span class="info-label">Gender:</span>
                <span class="info-value">${character.sex === 'm' ? 'Male' : 'Female'}</span>
            </div>
            <div class="logo-container-inline">
                <img src="nui://gang_spawns/html/img/void.png" alt="Void Logo" class="server-logo-inline"
                     onerror="console.log('Void logo failed to load'); this.src='nui://gang_spawns/html/img/default.png';"
                     onload="console.log('Void logo loaded successfully');">
            </div>
        `;

        infoContainer.html(infoHtml);
    };

    // Event handlers
    $(document).ready(function() {
        // Select character button
        $("#select-btn").click(function() {
            if (selectedCharacter) {
                $.post(`https://${GetParentResourceName()}/selectCharacter`, JSON.stringify({
                    charid: selectedCharacter.id
                }));
            }
        });

        // ESC key to close
        $(document).keyup(function(e) {
            if (e.keyCode === 27) { // ESC
                $.post(`https://${GetParentResourceName()}/closeUI`, JSON.stringify({}));
            }
        });
    });

    // Message handler
    window.addEventListener("message", function(event) {
        console.log('[MULTICHAR] Received message:', event.data);

        switch (event.data.action) {
            case "showOverlay":
                CharacterOverlay.Show(event.data.characters);
                break;
            case "hideOverlay":
                CharacterOverlay.Hide();
                break;
        }
    });

})();
