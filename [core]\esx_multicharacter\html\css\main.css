@import url("https://fonts.googleapis.com/css2?family=Raleway:wght@300;600&display=swap");

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    user-select: none;
    font-family: "Raleway", sans-serif;
}

html, body {
    overflow: hidden;
    background: transparent;
    color: white;
}

/* Main character overlay */
.character-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: transparent; /* Make background transparent */
    z-index: 9999;
    flex-direction: row;
    pointer-events: none; /* Allow clicks to pass through to game */
    justify-content: space-between;
    align-items: center;
    padding: 0 50px;
}

/* Left side - Character list panel */
.character-list-panel {
    width: 550px;
    height: 800px;
    background: transparent;
    border: none;
    border-radius: 12px;
    padding: 40px;
    box-shadow: none;
    backdrop-filter: none;
    pointer-events: auto;
    position: absolute;
    left: 50px;
    top: 50%;
    transform: translateY(-50%);
}

/* Right side - Character info panel */
.character-info-panel {
    width: 550px;
    height: 800px;
    background: transparent;
    border: none;
    border-radius: 12px;
    padding: 40px;
    box-shadow: none;
    backdrop-filter: none;
    display: flex;
    flex-direction: column;
    pointer-events: auto;
    position: absolute;
    right: 50px;
    top: 50%;
    transform: translateY(-50%);
}



/* Panel headers */
.panel-header {
    font-size: 32px;
    font-weight: 600;
    text-align: center;
    margin-bottom: 30px;
    color: #fff;
    background: rgba(0, 0, 0, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    padding: 15px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

/* Character list items */
.character-item {
    background: rgba(40, 40, 40, 0.9);
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.character-item:hover {
    border-color: rgba(255, 255, 255, 0.3);
    background: rgba(60, 60, 60, 0.9);
}

.character-item.selected {
    border-color: #4CAF50;
    background: rgba(76, 175, 80, 0.6);
}

.character-name {
    font-weight: 600;
    font-size: 22px;
    margin-bottom: 8px;
    color: #fff;
}

.character-job {
    font-size: 18px;
    color: #fff;
}

/* Character info display */
#character-info {
    flex: 1;
    padding: 20px;
    background: rgba(0, 0, 0, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.no-selection {
    text-align: center;
    color: #888;
    font-style: italic;
    margin-top: 50px;
}

.info-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
    padding: 10px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.info-label {
    font-weight: 600;
    color: #ccc;
    font-size: 20px;
}

.info-value {
    color: #fff;
    font-size: 20px;
}

/* Inline job logo container styling (inside character info) */
.logo-container-inline {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 30px;
    margin-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    min-height: 140px;
}

.server-logo-inline {
    max-width: 250px;
    max-height: 125px;
    width: auto;
    height: auto;
    object-fit: contain;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.5));
    border-radius: 8px;
}

/* Select button */
#select-btn {
    background: #dc3545;
    color: white;
    border: none;
    padding: 16px 32px;
    border-radius: 8px;
    font-size: 20px;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.3s ease;
}

#select-btn:hover {
    background: #c82333;
}

