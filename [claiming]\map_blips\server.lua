print("^2[MAP_BLIPS] Safe Zone System - Server Started^7")

-- Server-side safe zone management
RegisterNetEvent('map_blips:playerEnteredSafeZone')
AddEventHandler('map_blips:playerEnteredSafeZone', function(zoneName)
    local src = source
    print(string.format("^3[SAFE ZONE] Player %s entered: %s^7", GetPlayerName(src), zoneName))
end)

RegisterNetEvent('map_blips:playerLeftSafeZone')
AddEventHandler('map_blips:playerLeftSafeZone', function()
    local src = source
    print(string.format("^3[SAFE ZONE] Player %s left safe zone^7", GetPlayerName(src)))
end)
