Vue.component('message', {

  template: '#message_template',

  data() {

    return {};

  },

  computed: {

    textEscaped() {

      let s = this.template ? this.template : this.templates[this.templateId];



      if (this.template) {
        this.templateId = -1;

      }


      if (this.templateId == CONFIG.defaultTemplateId

        &&
        this.args.length == 1) {

        s = this.templates[CONFIG.defaultAltTemplateId] //Swap out default template :/

      }

      s = s.replace(/{(\d+)}/g, (match, number) => {

        const argEscaped = this.args[number] != undefined ? this.escape(this.args[number]) : match
        const title = this.title || false

        if (number == 0 && this.color) {
          return this.colorizeOld(title, argEscaped);
        }

        return argEscaped;

      });

      return this.colorize(s);

    },

  },

  methods: {

    colorizeOld(title, str) {
      if (this.systemMessage) {
        if (this.systemColour) {
          return `<span style="color: rgb(${this.color[0]}, ${this.color[1]}, ${this.color[2]})">${str.toUpperCase()} </span>`
        } else {
          return `<span>${str.toUpperCase()}</span>`
        }
      } else {
        if (title) {
          return `<span class="headtesting" style="background-color: rgb(${this.color[0]}, ${this.color[1]}, ${this.color[2]})">${title}</span><span>${str}</span>`
        }else {
          return `<span style="color: rgb(${this.color[0]}, ${this.color[1]}, ${this.color[2]})">${str}</span>`
        }
      }
    },

    colorize(str) {
      var s
      if (this.systemMessage) {
        if (this.systemColour) {
          s = `<span>` + (str.toUpperCase().replace(/\^([0-9])/g, (str, color) => `</span><span class="color-${color}">`)) + "</span>";
        } else {
          s = `<span>` + (str.replace(/\^([0-9])/g, "").toUpperCase()) + "</span>";
        }
      } else {
        s = `<span>` + (str.replace(/\^([0-9])/g, (str, color) => `</span><span class="color-${color}">`)) + "</span>";
      }

      const styleDict = {

        '*': 'font-weight: bold;',
        '_': 'text-decoration: underline;',
        '~': 'text-decoration: line-through;',
        '=': 'text-decoration: underline line-through;',
        'r': 'text-decoration: none;font-weight: normal;',

      };

      const styleRegex = /\^(\_|\*|\=|\~|\/|r)(.*?)(?=$|\^r|<\/em>)/;

      while (s.match(styleRegex)) {
        s = s.replace(styleRegex, (str, style, inner) => `<em style="${styleDict[style]}">${inner}</em>`)
      }

      return s.replace(/<span[^>]*><\/span[^>]*>/g, '');
    },

    escape(unsafe) {

      return String(unsafe)
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#039;');
    },

  },

  props: {

    templates: {

      type: Object,

    },

    args: {

      type: Array,

    },

    title: {
      type: String,
    },

    template: {

      type: String,

      default: null,

    },

    templateId: {

      type: String,

      default: CONFIG.defaultTemplateId,

    },

    multiline: {

      type: Boolean,

      default: false,

    },

    color: {
      type: Array,
      default: false,
    },

    systemMessage: {
      type: Boolean,
      default: false,
    },

    systemColour: {
      type: Boolean,
      default: false,
    }

  },

});