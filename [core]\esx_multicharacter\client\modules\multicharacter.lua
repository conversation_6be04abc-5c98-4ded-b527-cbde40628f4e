---@diagnostic disable: duplicate-set-field
Multicharacter = {}
Multicharacter._index = Multicharacter
Multicharacter.canRelog = true
Multicharacter.Characters = {}
Multicharacter.hidePlayers = false

function Multicharacter:SetupCamera()
    self.cam = CreateCam("DEFAULT_SCRIPTED_CAMERA", true)
    SetCamActive(self.cam, true)
    RenderScriptCams(true, false, 1, true, true)

    -- Position camera to center on character's face with close up view from the front at eye level
    local pedCoords = GetEntityCoords(self.playerPed)
    local pedHeading = GetEntityHeading(self.playerPed)

    -- Calculate camera position in front of the ped (facing the ped) at eye level
    local cameraDistance = 1.8 -- Closer distance for more zoom
    local cameraHeight = 0.65  -- Eye level height (same as target point)

    local radians = math.rad(pedHeading) -- Face the front of the ped (removed +180)
    local camX = pedCoords.x + (math.sin(radians) * cameraDistance)
    local camY = pedCoords.y + (math.cos(radians) * cameraDistance)
    local camZ = pedCoords.z + cameraHeight

    SetCamCoord(self.cam, camX, camY, camZ)
    PointCamAtCoord(self.cam, pedCoords.x, pedCoords.y, pedCoords.z + 0.65) -- Look at ped's face at same height as camera
    SetCamFov(self.cam, 30.0) -- More zoomed in field of view
end

function Multicharacter:AwaitFadeIn()
    print("^3[esx_multicharacter] AwaitFadeIn started^7")
    local timeout = GetGameTimer() + 5000 -- 5 second timeout

    while IsScreenFadingIn() and GetGameTimer() < timeout do
        Wait(200)
    end

    if GetGameTimer() >= timeout then
        print("^1[esx_multicharacter] AwaitFadeIn timeout - forcing completion^7")
    else
        print("^2[esx_multicharacter] AwaitFadeIn completed normally^7")
    end
end

function Multicharacter:AwaitFadeOut()
    while IsScreenFadingOut() do
        Wait(200)
    end
end

function Multicharacter:DestoryCamera()
    if self.cam then
        SetCamActive(self.cam, false)
        RenderScriptCams(false, false, 0, true, true)
        self.cam = nil
    end
end

local HiddenCompents = {}

local function HideComponents(hide)
    local components = {11, 12, 21}
    for i = 1, #components do
        if hide then
            local size = GetHudComponentSize(components[i])
            if size.x > 0 or size.y > 0 then
                HiddenCompents[components[i]] = size
                SetHudComponentSize(components[i], 0.0, 0.0)
            end
        else
            if HiddenCompents[components[i]] then
                local size = HiddenCompents[components[i]]
                SetHudComponentSize(components[i], size.x, size.z)
                HiddenCompents[components[i]] = nil
            end
        end
    end
    DisplayRadar(not hide)
end

function Multicharacter:HideHud(hide)
    self.hidePlayers = true

    MumbleSetVolumeOverride(ESX.PlayerId, 0.0)
    HideComponents(hide)
end

function Multicharacter:SetupCharacters()
    ESX.PlayerLoaded = false
    ESX.PlayerData = {}

    self.spawned = false

    self.playerPed = PlayerPedId()
    self.spawnCoords = Config.Spawn[ESX.Math.Random(1,#Config.Spawn)]

    SetEntityCoords(self.playerPed, self.spawnCoords.x, self.spawnCoords.y, self.spawnCoords.z, true, false, false, false)
    SetEntityHeading(self.playerPed, self.spawnCoords.w)

    SetPlayerControl(ESX.PlayerId, false, 0)
    self:SetupCamera()
    self:HideHud(true)

    ShutdownLoadingScreen()
    ShutdownLoadingScreenNui()
    TriggerEvent("esx:loadingScreenOff")

    SetTimeout(200, function()
        TriggerServerEvent("esx_multicharacter:SetupCharacters")
    end)
end

function Multicharacter:GetSkin()
    local character = self.Characters[self.tempIndex]
    local skin = character and character.skin or Config.Default
    if not character.model then
        if character.sex == TranslateCap("female") then
            skin.sex = 1
        else
            skin.sex = 0
        end
    end
    return skin
end

function Multicharacter:SpawnTempPed()
    self.canRelog = false
    local skin = self:GetSkin()
    ESX.SpawnPlayer(skin, self.spawnCoords, function()
        DoScreenFadeIn(600)
        self.playerPed = PlayerPedId()
    end)
end

function Multicharacter:ChangeExistingPed()
    local newCharacter = self.Characters[self.tempIndex]
    local spawnedCharacter = self.Characters[self.spawned]

    if spawnedCharacter and spawnedCharacter.model then
        local model = ESX.Streaming.RequestModel(newCharacter.model)
        if model then
            SetPlayerModel(ESX.playerId, newCharacter.model)
            SetModelAsNoLongerNeeded(newCharacter.model)
        end
    end

    TriggerEvent("skinchanger:loadSkin", newCharacter.skin)
end

function Multicharacter:PrepForUI()
    FreezeEntityPosition(self.playerPed, true)
    SetPedAoBlobRendering(self.playerPed, true)
    SetEntityAlpha(self.playerPed, 255, false)
end

function Multicharacter:CloseUI()
    SendNUIMessage({
        action = "hideOverlay",
    })
    SetNuiFocus(false, false)
end

function Multicharacter:SetupCharacter(index)
    local character = self.Characters[index]
    self.tempIndex = index

    if not self.spawned then
        self:SpawnTempPed()
    elseif character and character.skin then
        self:ChangeExistingPed()
    end

    self.spawned = index
    self.playerPed = PlayerPedId()
    self:PrepForUI()

    -- Update camera position for character positioning
    self:SetupCamera()
end

function Multicharacter:SetupUI(characters, slots)
    DoScreenFadeOut(0)

    self.Characters = characters
    self.slots = slots

    local Character = next(self.Characters)
    if not Character then
        self.canRelog = false

        ESX.SpawnPlayer(Config.Default, self.spawnCoords, function()
            DoScreenFadeIn(400)
            self:AwaitFadeIn()

            self.playerPed = PlayerPedId()
            SetPedAoBlobRendering(self.playerPed, false)
            SetEntityAlpha(self.playerPed, 0, false)

            TriggerServerEvent("esx_multicharacter:CharacterChosen", 1, true)
            TriggerEvent("esx_identity:showRegisterIdentity")
        end)
    else
        DoScreenFadeIn(400)
        self:AwaitFadeIn()

        -- Show our custom overlay instead of esx_context menu
        SendNUIMessage({
            action = "showOverlay",
            characters = characters
        })

        SetNuiFocus(true, true)

        -- Setup first character by default
        local firstChar = next(characters)
        if firstChar then
            self:SetupCharacter(firstChar)
        end
    end
end

function Multicharacter:LoadSkinCreator(skin)
    print("^2[esx_multicharacter] LoadSkinCreator called^7")

    TriggerEvent("skinchanger:loadSkin", skin, function()
        print("^3[esx_multicharacter] Skin loaded, fading in and opening skin menu^7")
        DoScreenFadeIn(600)
        SetPedAoBlobRendering(self.playerPed, true)
        ResetEntityAlpha(self.playerPed)

        print("^3[esx_multicharacter] Triggering esx_skin:openSaveableMenu^7")
        TriggerEvent("esx_skin:openSaveableMenu", function()
            print("^2[esx_multicharacter] Skin menu completed (save callback)^7")
            Multicharacter.finishedCreation = true
        end, function()
            print("^2[esx_multicharacter] Skin menu completed (cancel callback)^7")
            Multicharacter.finishedCreation = true
        end)
    end)
end

function Multicharacter:SetDefaultSkin(playerData)

    local skin = Config.Default[playerData.sex]
    skin.sex = playerData.sex == "m" and 0 or 1

    local model = skin.sex == 0 and `mp_m_freemode_01` or `mp_f_freemode_01`
    ---@diagnostic disable-next-line: cast-local-type
    model = ESX.Streaming.RequestModel(model)

    if not model then
        return
    end

    SetPlayerModel(ESX.playerId, model)
    SetModelAsNoLongerNeeded(model)
    self.playerPed = PlayerPedId()

    self:LoadSkinCreator(skin)
end

function Multicharacter:Reset()
    self.Characters = {}
    self.tempIndex = nil
    self.playerPed = PlayerPedId()
    self.hidePlayers = false
    self.slots = nil

    SetTimeout(10000, function()
        self.canRelog = true
    end)
end

-- NUI Callbacks for our custom interface
RegisterNUICallback('previewCharacter', function(data, cb)
    local charid = data.charid
    if charid and Multicharacter.Characters[charid] then
        Multicharacter:SetupCharacter(charid)
    end
    cb('ok')
end)

RegisterNUICallback('selectCharacter', function(data, cb)
    local charid = data.charid
    if charid then
        Multicharacter:CloseUI()
        TriggerServerEvent("esx_multicharacter:CharacterChosen", charid, false)
    end
    cb('ok')
end)

RegisterNUICallback('closeUI', function(data, cb)
    Multicharacter:CloseUI()
    cb('ok')
end)

function Multicharacter:PlayerLoaded(playerData, isNew, skin)
    print("^2[esx_multicharacter] PlayerLoaded called^7")
    print("^3[esx_multicharacter] isNew: " .. tostring(isNew) .. "^7")
    print("^3[esx_multicharacter] skin: " .. tostring(skin and "present" or "nil") .. "^7")

    DoScreenFadeOut(750)
    self:AwaitFadeOut()

    local esxSpawns = ESX.GetConfig().DefaultSpawns
    local spawn = esxSpawns[math.random(1, #esxSpawns)]

    if not isNew and playerData.coords then
        spawn = playerData.coords
    end

    if isNew or not skin or #skin == 1 then
        print("^3[esx_multicharacter] New character or no skin - setting up character creation^7")
        self.finishedCreation = false
        self:SetDefaultSkin(playerData)

        print("^3[esx_multicharacter] Waiting for character creation to finish^7")
        while not self.finishedCreation do
            Wait(200)
        end

        print("^3[esx_multicharacter] Character creation finished, getting skin^7")
        skin = exports["skinchanger"]:GetSkin()
        DoScreenFadeOut(500)
        self:AwaitFadeOut()

    elseif not isNew then
        print("^3[esx_multicharacter] Existing character - loading skin^7")
        TriggerEvent("skinchanger:loadSkin", skin or self.Characters[self.spawned].skin)
    end

    print("^3[esx_multicharacter] Destroying camera and spawning player^7")
    self:DestoryCamera()

    print("^3[esx_multicharacter] Calling ESX.SpawnPlayer with spawn coords^7")
    ESX.SpawnPlayer(skin, spawn, function()
        print("^2[esx_multicharacter] ESX.SpawnPlayer callback triggered^7")

        self:HideHud(false)
        SetPlayerControl(ESX.playerId, true, 0)

        self.playerPed = PlayerPedId()
        FreezeEntityPosition(self.playerPed, false)
        SetEntityCollision(self.playerPed, true, true)

        print("^3[esx_multicharacter] Fading screen in^7")
        DoScreenFadeIn(750)

        print("^3[esx_multicharacter] Waiting for fade in to complete^7")
        self:AwaitFadeIn()

        -- SAFETY: Force screen fade in if it's still black after 2 seconds
        SetTimeout(2000, function()
            if IsScreenFadedOut() then
                print("^1[esx_multicharacter] EMERGENCY: Screen still black, forcing fade in^7")
                DoScreenFadeIn(500)
                SetTimeout(1000, function()
                    if IsScreenFadedOut() then
                        print("^1[esx_multicharacter] CRITICAL: Forcing immediate screen visibility^7")
                        DoScreenFadeIn(0) -- Instant fade in
                    end
                end)
            end
        end)

        print("^3[esx_multicharacter] Triggering spawn events^7")
        TriggerServerEvent("esx:onPlayerSpawn")
        TriggerEvent("esx:onPlayerSpawn")
        TriggerEvent("esx:restoreLoadout")

        print("^2[esx_multicharacter] Player spawn complete, resetting^7")
        self:Reset()
    end)
end

-- EMERGENCY BLACK SCREEN FIX COMMAND
RegisterCommand("fixblackscreen", function()
    print("^1[esx_multicharacter] EMERGENCY: Fixing black screen^7")

    -- Force screen fade in
    DoScreenFadeIn(0)

    -- Unfreeze player
    local playerPed = PlayerPedId()
    FreezeEntityPosition(playerPed, false)
    SetEntityCollision(playerPed, true, true)
    SetPlayerControl(ESX.playerId, true, 0)

    -- Hide any UI elements
    SetNuiFocus(false, false)
    SetNuiFocusKeepInput(false)

    -- Clear any stuck states
    ClearTimecycleModifier()

    print("^2[esx_multicharacter] Black screen fix applied^7")
end, false)
