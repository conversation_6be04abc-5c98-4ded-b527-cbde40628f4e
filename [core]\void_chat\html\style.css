/* html/style.css */
body {
  margin: 0;
  padding: 0;
  background: transparent;
  overflow: hidden;
}
#chat-container {
  position: absolute;
  left: 1%;
  top: 50%;
  transform: translateY(-50%);
  width: 300px;
  font-family: Arial, sans-serif;
  font-size: 16px;
  color: white;
}
.chat-message {
  margin-bottom: 5px;
  word-wrap: break-word;
}
/* input box in middle-left */
#chat-input {
  position: absolute;
  left: 1%;
  bottom: 25%;
  display: flex;
  align-items: center;
}
#chat-input.hidden {
  display: none;
}
#chat-input input {
  background: transparent;
  border: none;
  border-bottom: 1px solid white;
  color: white;
  font-size: 16px;
  outline: none;
  width: 200px;
}
#chat-input span {
  margin-right: 5px;
  color: white;
  font-size: 16px;
}
