/* reset */
html, body { margin:0; padding:0; background:transparent; overflow:hidden; }

/* main container */
#voidClaimingUI {
  position: absolute;
  top: 50%; left: 50%;
  transform: translate(-50%, -50%);
  width: 50vw; height: 50vh;
  background: #000;            /* fully opaque black */
  border-radius: 16px;
  box-shadow: 0 0 20px rgba(0,0,0,0.6);
  display: none;               /* hidden until opened */
  z-index: 9999;
}

/* header */
#voidClaimingUI .ui-header {
  display: flex; align-items: center;
  padding: 12px 16px;
  background: #111;            /* slightly lighter header */
  border-top-left-radius:16px;
  border-top-right-radius:16px;
}

/* logo */
#voidClaimingUI .ui-logo {
  width: 40px; height: 40px;
  object-fit: contain;
  margin-right: 12px;
}

/* title */
#voidClaimingUI .ui-title {
  flex: 1;
  margin: 0;
  font-family: 'Segoe UI', Tahoma, sans-serif;
  font-size: 24px;
  font-weight: 700;            /* bold */
  color: #fff;
}

/* close button */
#voidClaimingUI .ui-close-button {
  background: none;
  border: none;
  color: #fff;
  font-size: 28px;
  line-height: 1;
  cursor: pointer;
}

/* body */
#voidClaimingUI .ui-body {
  background: #000;            /* body same solid black */
  padding: 16px;
  color: #ddd;
  height: calc(100% - 64px);   /* leave room for header */
  overflow-y: auto;
}
