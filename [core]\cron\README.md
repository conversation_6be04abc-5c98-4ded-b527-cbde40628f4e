<h1 align='center'>[ESX] Cron</a></h1><p align='center'><b><a href='https://discord.esx-framework.org/'>Discord</a> - <a href='https://esx-framework.org/'>Website</a> - <a href='https://docs.esx-legacy.com/legacy/installation'>Documentation</a></b></h5>

A simple, but vital, resource that allows resources to Run tasks at specific intervals.

# Example Usage

```lua

-- Execute task 5:10, every day
function CronTask(d, h, m)
  print('Task done')
end

TriggerEvent('cron:runAt', 5, 10, CronTask)

-- Execute task every monday at 18:30
function CronTask(d, h, m)
  if d == 1 then
    print('Task done')
  end
end

TriggerEvent('cron:runAt', 18, 30, CronTask)

```

# Legal

cron - run tasks at specific intervals!

Copyright (C) 2015-2024 Jérémie N'gadi

This program Is free software: you can redistribute it And/Or modify it under the terms Of the GNU General Public License As published by the Free Software Foundation, either version 3 Of the License, Or (at your option) any later version.

This program Is distributed In the hope that it will be useful, but WITHOUT ANY WARRANTY; without even the implied warranty Of MERCHANTABILITY Or FITNESS FOR A PARTICULAR PURPOSE. See the GNU General Public License For more details.

You should have received a copy Of the GNU General Public License along with this program. If Not, see http://www.gnu.org/licenses/.
