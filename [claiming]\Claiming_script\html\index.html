<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Void Claiming System</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
  <style>
    :root {
      --primary: #3b82f6;
      --primary-dark: #2563eb;
      --secondary: #6b7280;
      --success: #3b82f6;
      --warning: #3b82f6;
      --danger: #3b82f6;
      --dark: #1a1a1a;
      --dark-light: #2a2a2a;
      --dark-lighter: #3a3a3a;
      --text: #ffffff;
      --text-muted: #a1a1aa;
      --border: #404040;
      --blue-border: #3b82f6;
      --glass: rgba(26, 26, 26, 0.95);
      --glass-light: rgba(42, 42, 42, 0.8);
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    html, body {
      margin: 0;
      padding: 0;
      background: transparent;
      overflow: hidden;
      font-family: 'Inter', sans-serif;
    }

    #voidClaimingUI {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 80vw;
      max-width: 1200px;
      height: 80vh;
      background: transparent;
      border: 1px solid var(--border);
      border-radius: 16px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
      display: none;
      flex-direction: column;
      color: var(--text);
      overflow: hidden;
      animation: slideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    @keyframes slideIn {
      from {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.95);
      }
      to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
      }
    }
    .ui-header {
      height: 80px;
      background: var(--dark-light);
      border-top-left-radius: 16px;
      border-top-right-radius: 16px;
      border-bottom: 1px solid var(--border);
      display: flex;
      align-items: center;
      padding: 0 32px;
      position: relative;
    }



    .ui-logo {
      width: 48px;
      height: 48px;
      object-fit: contain;
      margin-right: 16px;
      filter: drop-shadow(0 4px 8px rgba(0,0,0,0.3));
    }

    .ui-title {
      font-size: 24px;
      font-weight: 700;
      color: white;
      text-shadow: 0 2px 4px rgba(0,0,0,0.3);
      z-index: 1;
    }

    .ui-close-button {
      margin-left: auto;
      background: var(--dark-lighter);
      border: 2px solid var(--border);
      color: var(--text-muted);
      font-size: 20px;
      cursor: pointer;
      width: 40px;
      height: 40px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
      z-index: 1;
    }

    .ui-close-button:hover {
      background: var(--blue-border);
      border-color: var(--blue-border);
      color: white;
      transform: scale(1.05);
    }

    .ui-body {
      flex: 1;
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 32px;
      padding: 32px;
      background: var(--dark);
    }
    .controls {
      display: flex;
      flex-direction: column;
      gap: 24px;
    }

    .control-section {
      background: var(--dark-light);
      border: 1px solid var(--border);
      border-radius: 16px;
      padding: 24px;
      transition: all 0.3s ease;
    }

    .control-section:hover {
      border-color: var(--blue-border);
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
    }

    .section-title {
      font-size: 18px;
      font-weight: 600;
      color: var(--text);
      margin-bottom: 16px;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .section-title i {
      color: var(--blue-border);
    }

    .control-group {
      margin-bottom: 20px;
    }

    .control-group label {
      display: block;
      margin-bottom: 8px;
      font-size: 14px;
      font-weight: 500;
      color: var(--text);
    }

    .control-group select {
      width: 100%;
      padding: 12px 16px;
      border: 1px solid var(--border);
      border-radius: 12px;
      background: var(--dark-lighter);
      color: var(--text);
      font-size: 14px;
      font-family: inherit;
      transition: all 0.2s ease;
    }

    .control-group select:focus {
      outline: none;
      border-color: var(--blue-border);
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .status-group {
      display: flex;
      gap: 12px;
      flex-wrap: wrap;
    }

    .status-group button {
      flex: 1;
      min-width: 120px;
      padding: 12px 20px;
      border: none;
      border-radius: 12px;
      font-size: 14px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      position: relative;
      overflow: hidden;
    }

    .status-group button::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
      transition: left 0.5s;
    }

    .status-group button:hover::before {
      left: 100%;
    }

    .start, .end, .pause, .goto, .car-warfare {
      background: var(--dark-lighter);
      color: var(--text);
      border: 2px solid var(--blue-border);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .start:hover, .end:hover, .pause:hover, .goto:hover, .car-warfare:hover {
      background: var(--blue-border);
      color: white;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
    }

    .car-warfare {
      cursor: pointer;
      pointer-events: auto;
    }

    .car-warfare.enabled {
      background: #97f57d;
      border-color: #97f57d;
      color: #000;
    }

    .car-warfare.enabled:hover {
      background: #7dd65a;
      border-color: #7dd65a;
    }
    .right-panel {
      display: flex;
      flex-direction: column;
      gap: 24px;
    }

    .live-status {
      background: var(--dark-light);
      border: 1px solid var(--border);
      border-radius: 16px;
      padding: 24px;
      transition: all 0.3s ease;
    }

    .live-status:hover {
      border-color: var(--blue-border);
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
    }

    .live-status h2 {
      margin: 0 0 20px;
      font-size: 18px;
      font-weight: 600;
      color: var(--text);
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .live-status h2 i {
      color: var(--blue-border);
    }

    .status-grid {
      display: grid;
      gap: 16px;
    }

    .status-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      background: var(--dark-lighter);
      border-radius: 12px;
      border: 1px solid var(--border);
      transition: all 0.2s ease;
    }

    .status-item:hover {
      border-color: var(--blue-border);
      transform: translateX(4px);
    }

    .status-label {
      font-size: 14px;
      color: var(--text-muted);
      font-weight: 500;
    }

    .status-value {
      font-size: 14px;
      font-weight: 600;
      color: white;
      padding: 4px 12px;
      background: var(--blue-border);
      border-radius: 8px;
    }

    .logs-section {
      background: var(--dark-light);
      border: 1px solid var(--border);
      border-radius: 16px;
      padding: 24px;
      display: flex;
      flex-direction: column;
      max-height: 600px;
      transition: all 0.3s ease;
    }

    .logs-section:hover {
      border-color: var(--blue-border);
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
    }

    .logs-section h3 {
      margin: 0 0 16px;
      font-size: 18px;
      font-weight: 600;
      color: var(--text);
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .logs-section h3 i {
      color: var(--blue-border);
    }

    .logs-container {
      flex: 1;
      overflow-y: auto;
      border-radius: 12px;
      background: var(--dark);
    }

    .logs-container::-webkit-scrollbar {
      width: 6px;
    }

    .logs-container::-webkit-scrollbar-track {
      background: var(--dark-lighter);
      border-radius: 3px;
    }

    .logs-container::-webkit-scrollbar-thumb {
      background: var(--blue-border);
      border-radius: 3px;
    }

    .logs-section table {
      width: 100%;
      border-collapse: collapse;
    }

    .logs-section th, .logs-section td {
      padding: 12px 16px;
      text-align: left;
      font-size: 13px;
    }

    .logs-section th {
      font-weight: 600;
      color: var(--blue-border);
      background: var(--dark-lighter);
      border-bottom: 2px solid var(--border);
      position: sticky;
      top: 0;
      z-index: 1;
    }

    .logs-section td {
      color: var(--text-muted);
      border-bottom: 1px solid var(--border);
      transition: all 0.2s ease;
    }

    .logs-section tr:hover td {
      background: var(--dark-lighter);
      color: var(--text);
    }

    .log-status {
      padding: 4px 8px;
      border-radius: 6px;
      font-size: 11px;
      font-weight: 600;
      text-transform: uppercase;
    }

    .log-status.start, .log-status.end, .log-status.pause, .log-status.claim {
      background: rgba(59, 130, 246, 0.2);
      color: var(--blue-border);
      border: 1px solid rgba(59, 130, 246, 0.3);
    }
  </style>
</head>
<body>
  <div id="voidClaimingUI">
    <div class="ui-header">
      <img src="img/logo.png" class="ui-logo" alt="Void Logo"/>
      <div class="ui-title">
        Void Claiming System
      </div>
      <button class="ui-close-button" onclick="closeUI()">
        <i class="fas fa-times"></i>
      </button>
    </div>
    <div class="ui-body">
      <div class="controls">
        <!-- Configuration Section -->
        <div class="control-section">
          <div class="section-title">
            <i class="fas fa-cog"></i>
            Configuration
          </div>
          <div class="control-group">
            <label for="locationSelect">
              <i class="fas fa-map-marker-alt"></i>
              Claim Location
            </label>
            <select id="locationSelect">
              <option value="">-- Select Location --</option>
            </select>
          </div>
          <div class="control-group">
            <label for="modeSelect">
              <i class="fas fa-gamepad"></i>
              Game Mode
            </label>
            <select id="modeSelect">
              <option value="">-- Select Mode --</option>
            </select>
          </div>
        </div>

        <!-- Control Section -->
        <div class="control-section">
          <div class="section-title">
            <i class="fas fa-cogs"></i>
            Actions
          </div>
          <div class="status-group">
            <button class="start">
              <i class="fas fa-play"></i>
              Start Round
            </button>
            <button class="end">
              <i class="fas fa-stop"></i>
              End Round
            </button>
            <button class="pause">
              <i class="fas fa-pause"></i>
              Pause Round
            </button>
            <button class="goto" onclick="goToClaimLocation()">
              <i class="fas fa-map-marker-alt"></i>
              Go To
            </button>
            <button class="car-warfare" onclick="toggleCarWarfare()">
              <i class="fas fa-car"></i>
              Car Warfare
            </button>
          </div>
        </div>
      </div>

      <div class="right-panel">


        <!-- Activity Logs Section -->
        <div class="logs-section">
          <h3>
            <i class="fas fa-history"></i>
            Activity Logs
          </h3>
          <div class="logs-container">
            <table>
              <thead>
                <tr>
                  <th>Player</th>
                  <th>Group</th>
                  <th>Action</th>
                  <th>Mode</th>
                  <th>Location</th>
                </tr>
              </thead>
              <tbody></tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
  <script>
    const ui = document.getElementById('voidClaimingUI');
    const locSel = document.getElementById('locationSelect');
    const modeSel = document.getElementById('modeSelect');
    const startBtn = document.querySelector('.start');
    const endBtn = document.querySelector('.end');
    const pauseBtn = document.querySelector('.pause');
    const logsBody = document.querySelector('.logs-section tbody');
    const liveLocation = document.getElementById('liveLocation');
    const liveMode = document.getElementById('liveMode');
    const liveCreator = document.getElementById('liveCreator');

    // Enhanced animations and interactions
    function showNotification(message, type = 'info') {
      const notification = document.createElement('div');
      notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 16px 24px;
        background: var(--blue-border);
        color: white;
        border-radius: 12px;
        font-weight: 600;
        z-index: 10000;
        animation: slideInRight 0.3s ease;
        box-shadow: 0 8px 25px rgba(0,0,0,0.3);
      `;
      notification.textContent = message;
      document.body.appendChild(notification);

      setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => notification.remove(), 300);
      }, 3000);
    }

    function init(data) {
      // Enhanced location options with icons
      const locationOptions = data.locations.map(l =>
        `<option value="${l.key}">${l.label}</option>`
      ).join('');
      locSel.innerHTML = '<option value="">-- Select Location --</option>' + locationOptions;

      // Enhanced mode options with descriptions
      const modeDescriptions = {
        'any': 'All weapons allowed',
        'deagleonly': 'Desert Eagle only',
        'x2headdy': 'Deagle only - 2 tap headshots'
      };

      const modeOptions = data.modes.map(m =>
        `<option value="${m.key}" title="${modeDescriptions[m.key] || ''}">${m.label}</option>`
      ).join('');
      modeSel.innerHTML = '<option value="">-- Select Mode --</option>' + modeOptions;

      // Enhanced logs with status styling
      logsBody.innerHTML = data.logs.map(e => `
        <tr>
          <td>${e.name}</td>
          <td>${e.group}</td>
          <td><span class="log-status ${e.status.toLowerCase()}">${e.status}</span></td>
          <td>${e.mode || '-'}</td>
          <td>${e.location || '-'}</td>
        </tr>
      `).join('');

      updateLiveStatus(data.liveStatus);
    }

    function addLog(e) {
      const row = document.createElement('tr');
      row.style.animation = 'slideInLeft 0.3s ease';
      row.innerHTML = `
        <td>${e.name}</td>
        <td>${e.group}</td>
        <td><span class="log-status ${e.status.toLowerCase()}">${e.status}</span></td>
        <td>${e.mode || '-'}</td>
        <td>${e.location || '-'}</td>
      `;
      logsBody.insertBefore(row, logsBody.firstChild);

      // Limit logs to 50 entries
      while (logsBody.children.length > 50) {
        logsBody.removeChild(logsBody.lastChild);
      }
    }

    function updateLiveStatus(ls) {
      liveLocation.textContent = ls.location;
      liveMode.textContent = ls.mode;
      liveCreator.textContent = ls.creator;

      // Add pulse animation for active status
      if (ls.location !== 'N/A') {
        liveLocation.style.animation = 'pulse 2s infinite';
      } else {
        liveLocation.style.animation = 'none';
      }
    }

    function validateSelection() {
      const location = locSel.value;
      const mode = modeSel.value;

      if (!location || !mode) {
        return false;
      }
      return true;
    }

    // Event listeners with enhanced feedback
    window.addEventListener('message', ev => {
      const d = ev.data;
      if (d.action === 'init') init(d);
      if (d.action === 'addLog') addLog(d.entry);
      if (d.action === 'updateLive') updateLiveStatus(d.liveStatus);
      if (d.action === 'updateCarWarfare') updateCarWarfareButton(d.enabled);
      if (d.action === 'openUI') {
        ui.style.display = 'flex';

      }
    });

    function updateCarWarfareButton(enabled) {
      const button = document.querySelector('.car-warfare');
      if (button) {
        if (enabled) {
          button.classList.add('enabled');
        } else {
          button.classList.remove('enabled');
        }
      }
    }



    startBtn.onclick = () => {
      if (!validateSelection()) return;

      startBtn.style.transform = 'scale(0.95)';
      setTimeout(() => startBtn.style.transform = '', 150);

      try {
        fetch(`https://${GetParentResourceName()}/start`, {
          method: 'POST',
          headers: {'Content-Type': 'application/json'},
          body: JSON.stringify({ location: locSel.value, mode: modeSel.value })
        }).catch(error => {
          console.error('Start round error:', error);
        });
      } catch (error) {
        console.error('Start round fetch error:', error);
      }

    };

    endBtn.onclick = () => {
      endBtn.style.transform = 'scale(0.95)';
      setTimeout(() => endBtn.style.transform = '', 150);

      try {
        fetch(`https://${GetParentResourceName()}/end`, { method: 'POST' }).catch(error => {
          console.error('End round error:', error);
        });
      } catch (error) {
        console.error('End round fetch error:', error);
      }

    };

    pauseBtn.onclick = () => {
      pauseBtn.style.transform = 'scale(0.95)';
      setTimeout(() => pauseBtn.style.transform = '', 150);

      try {
        fetch(`https://${GetParentResourceName()}/pause`, { method: 'POST' }).catch(error => {
          console.error('Pause round error:', error);
        });
      } catch (error) {
        console.error('Pause round fetch error:', error);
      }

    };

    function goToClaimLocation() {
      const locationSelect = document.getElementById('locationSelect');
      const selectedLocation = locationSelect.value;

      if (!selectedLocation || selectedLocation === '') {
        return; // Silent fail, no alert needed
      }

      try {
        fetch(`https://${GetParentResourceName()}/goToLocation`, {
          method: 'POST',
          headers: {'Content-Type': 'application/json'},
          body: JSON.stringify({location: selectedLocation})
        }).catch(error => {
          console.error('Go To Location error:', error);
        });
      } catch (error) {
        console.error('Go To Location fetch error:', error);
      }
    }

    function toggleCarWarfare() {
      console.log('[CLAIMING] Car warfare button clicked');
      try {
        fetch(`https://${GetParentResourceName()}/toggleCarWarfareClient`, {
          method: 'POST',
          headers: {'Content-Type': 'application/json'},
          body: JSON.stringify({})
        }).then(response => {
          console.log('[CLAIMING] Car warfare toggle response:', response);
        }).catch(error => {
          console.error('Toggle Car Warfare error:', error);
        });
      } catch (error) {
        console.error('Toggle Car Warfare fetch error:', error);
      }
    }



    function closeUI() {
      ui.style.animation = 'slideOut 0.3s ease';
      setTimeout(() => {
        ui.style.display = 'none';
        ui.style.animation = '';
      }, 300);

      try {
        fetch(`https://${GetParentResourceName()}/closeUI`, {
          method: 'POST',
          headers: {'Content-Type': 'application/json'},
          body: '{}'
        }).catch(error => {
          console.error('Close UI error:', error);
        });
      } catch (error) {
        console.error('Close UI fetch error:', error);
      }
    }

    // Add CSS animations
    const style = document.createElement('style');
    style.textContent = `
      @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
      }
      @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
      }
      @keyframes slideInLeft {
        from { transform: translateX(-20px); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
      }
      @keyframes slideOut {
        from { transform: translate(-50%, -50%) scale(1); opacity: 1; }
        to { transform: translate(-50%, -50%) scale(0.95); opacity: 0; }
      }
      @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.7; }
      }
    `;
    document.head.appendChild(style);

    // Keyboard shortcuts
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        closeUI();
      }
    });
  </script>
</body>
</html>
