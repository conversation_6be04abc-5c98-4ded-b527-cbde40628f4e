let selectedVehicle = null;
let availableVehicles = [];

// Vehicle images mapping (you can add more)
const vehicleImages = {
    'g81hr': 'https://via.placeholder.com/200x120/4bbef4/ffffff?text=G81HR',
    'r8hycade': 'https://via.placeholder.com/200x120/4bbef4/ffffff?text=R8+HYCADE',
    // Add more vehicle images as needed
};

window.addEventListener('message', function(event) {
    const data = event.data;
    
    switch(data.action) {
        case 'openGarage':
            openGarage(data.vehicles, data.garageName);
            break;
        case 'showNotification':
            showNotification(data.message, data.type);
            break;
    }
});

function openGarage(vehicles, garageName) {
    availableVehicles = vehicles;
    document.getElementById('garage-title').textContent = garageName;
    document.getElementById('garage-container').style.display = 'flex';
    
    populateVehicles();
    resetSelection();
}

function populateVehicles() {
    const container = document.getElementById('vehicles-container');
    container.innerHTML = '';
    
    availableVehicles.forEach(vehicle => {
        const vehicleElement = document.createElement('div');
        vehicleElement.className = 'vehicle-item';
        vehicleElement.onclick = () => selectVehicle(vehicle);
        
        vehicleElement.innerHTML = `
            <h4>${vehicle.name}</h4>
            <p>Click to select this vehicle</p>
        `;
        
        container.appendChild(vehicleElement);
    });
}

function selectVehicle(vehicle) {
    selectedVehicle = vehicle;
    
    // Update selection visual
    document.querySelectorAll('.vehicle-item').forEach(item => {
        item.classList.remove('selected');
    });
    event.currentTarget.classList.add('selected');
    
    // Update preview
    updateVehiclePreview(vehicle);
    
    // Enable spawn button
    document.getElementById('spawn-btn').disabled = false;
}

function updateVehiclePreview(vehicle) {
    const image = document.getElementById('vehicle-image');
    const name = document.getElementById('vehicle-name');
    const description = document.getElementById('vehicle-description');
    
    image.src = vehicleImages[vehicle.model] || 'https://via.placeholder.com/200x120/4bbef4/ffffff?text=Vehicle';
    name.textContent = vehicle.name;
    description.textContent = `Ready to spawn ${vehicle.name}`;
}

function resetSelection() {
    selectedVehicle = null;
    document.getElementById('vehicle-image').src = 'https://via.placeholder.com/200x120/666666/ffffff?text=No+Selection';
    document.getElementById('vehicle-name').textContent = 'Select a Vehicle';
    document.getElementById('vehicle-description').textContent = 'Choose from available vehicles';
    document.getElementById('spawn-btn').disabled = true;
}

function spawnSelectedVehicle() {
    if (!selectedVehicle) return;
    
    fetch(`https://${GetParentResourceName()}/spawnVehicle`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            model: selectedVehicle.model
        })
    }).then(response => response.text()).then(result => {
        if (result === 'success') {
            closeGarage();
        }
    });
}

function closeGarage() {
    document.getElementById('garage-container').style.display = 'none';
    
    fetch(`https://${GetParentResourceName()}/closeGarage`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({})
    });
}

function showNotification(message, type = 'error') {
    const notification = document.getElementById('notification');
    const text = document.getElementById('notification-text');
    
    text.textContent = message;
    notification.className = `notification ${type}`;
    notification.style.display = 'block';
    
    setTimeout(() => {
        notification.style.display = 'none';
    }, 3000);

)
// Close on ESC key
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        closeGarage();
    }
});