-- server.lua
ESX = nil
TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)

local heliRoundActive = false

RegisterCommand('modeheli', function(source, args, raw)
  local xPlayer = source > 0 and ESX.GetPlayerFromId(source) or nil
  if source == 0
    or (xPlayer and (xPlayer.getGroup() == 'admin'
                  or xPlayer.getGroup() == 'cl'
                  or xPlayer.getGroup() == 'owner')) then

    heliRoundActive = not heliRoundActive
    TriggerClientEvent('gang_heli_garage:roundStatus', -1, heliRoundActive)
    local msg = heliRoundActive and 'Helicopter round is now ACTIVE.' or 'Helicopter round is now INACTIVE.'
    TriggerClientEvent('chat:addMessage', -1, {
      args = { '[SYSTEM]', msg }
    })
  else
    TriggerClientEvent('chat:addMessage', source, {
      args = { '[SYSTEM]', 'Insufficient permissions.' }
    })
  end
end, false)
