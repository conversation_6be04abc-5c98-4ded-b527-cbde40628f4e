@import "https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap";
* {
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen,
    Ubuntu, Cantarell, Open Sans, Helvetica Neue, sans-serif;
  --primary: #156cef;
  --error: rgb(245, 58, 58);
  --green: #35c75a;
  --twitter: #1ca1f1;
  --sentColor: #0b93f6;
  --receiveColor: #e5e5ea;
  --appbackground: #090909;
}
body {
  margin: 0;
  height: 100vh;
  overflow: hidden;
}
#root {
  height: 100%;
  color: #fff;
  background-color: #111;
  display: flex;
  align-items: center;
  justify-content: end;
}
::-webkit-scrollbar {
  width: 0px;
}
._backgroundImageContainer_1sf4f_1 {
  width: 100%;
  height: 100%;
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
  opacity: 0.2;
}
._iconicLogo_1sf4f_21 {
  width: 700px;
  margin-bottom: 10px;
  filter: drop-shadow(0px 0px 10px rgb(0, 0, 0));
}
._middleContainer_1sf4f_33 {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #949494;
  font-weight: 400;
}
._middleContainer_1sf4f_33 p {
  margin: 0;
  padding: 0;
}
._topLeftContainer_1sf4f_71 {
  position: absolute;
  top: 0;
  left: 0;
  padding: 20px;
}
._topLeftContainer_1sf4f_71 > span {
  display: flex;
  flex-direction: row;
  align-items: center;
  font-weight: 500;
  font-size: 10pt;
  margin-bottom: 10px;
}
._topLeftContainer_1sf4f_71 > span i {
  color: #000;
  background-color: #fff;
  border-radius: 5px;
  width: 35px;
  height: 35px;
  font-size: 16pt;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
}
._bottomContainer_1sf4f_133 {
  position: absolute;
  left: 50%;
  bottom: 0;
  transform: translate(-50%);
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
._bottomContainer_1sf4f_133 > span {
  display: flex;
  flex-direction: row;
  align-items: center;
  font-weight: 500;
  font-size: 10pt;
  margin-bottom: 10px;
}
._bottomContainer_1sf4f_133 > span i {
  color: #000;
  width: 35px;
  height: 35px;
  font-size: 16pt;
  display: flex;
  align-items: center;
  justify-content: center;
}
._status_1sf4f_199 {
  background-color: #fff;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 35px;
}
#music-controls {
    position: absolute;
    top: 10px;
    right: 10px;
    display: flex;
    align-items: center;
    gap: 10px;
}

#pause-icon {
    cursor: pointer;
    font-size: 24px;
    color: white;
    background-color: rgba(0, 0, 0, 0.5);
    padding: 10px;
    border-radius: 5px;
    user-select: none;
}

#volume-slider {
    width: 100px;
    height: 8px;
    cursor: pointer;
}