-- config.lua

Config = {}

-- Position & size of the bar, relative to screen (0.0–1.0)
Config.Bar = {
  width  = 0.1399999,   -- 20% of screen width
  height = 0.0270,  -- 1.8% of screen height
  x      = 0.085,   -- center X (0.0 = left, 1.0 = right)
  y      = 0.98199   -- center Y (0.0 = top, 1.0 = bottom)
}

Config.Colors = {
  background = {75, 1, 1, 0.40},  --rgba(75, 1, 1, 0.34) solid grey
  foreground = {255,   0,   0, 255}   -- solid red
}



-- Hide the default health HUD component? (true/false)
Config.HideDefault = true
