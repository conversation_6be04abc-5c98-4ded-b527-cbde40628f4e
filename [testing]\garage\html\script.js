let garageOpen = false;

window.addEventListener('message', function(event) {
    const data = event.data;
    
    switch(data.action) {
        case 'openGarage':
            openGarage(data.vehicles);
            break;
        case 'closeGarage':
            closeGarage();
            break;
    }
});

function openGarage(vehicles) {
    garageOpen = true;
    document.getElementById('garage-container').classList.remove('hidden');
    populateVehicles(vehicles);
}

function closeGarage() {
    garageOpen = false;
    document.getElementById('garage-container').classList.add('hidden');
}

function populateVehicles(vehicles) {
    const tbody = document.getElementById('vehicles-tbody');
    tbody.innerHTML = '';
    
    vehicles.forEach(vehicle => {
        const row = document.createElement('tr');
        row.onclick = () => spawnVehicle(vehicle.model);
        
        const health = Math.floor(Math.random() * 16) + 85;
        
        row.innerHTML = `
            <td class="vehicle-name">${vehicle.name}</td>
            <td class="vehicle-plate">SAPPHIRE</td>
            <td class="vehicle-type">CAR</td>
            <td class="vehicle-health">${health}%</td>
        `;
        
        tbody.appendChild(row);
    });
}

function spawnVehicle(model) {
    fetch(`https://${GetParentResourceName()}/spawnVehicle`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            model: model
        })
    });
}

function closeGarageUI() {
    fetch(`https://${GetParentResourceName()}/closeGarage`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({})
    });
}

document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape' && garageOpen) {
        closeGarageUI();
    }
});