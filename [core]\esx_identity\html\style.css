* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    background: transparent;
    overflow: hidden;
    color: #ffffff;
    font-size: 14px;
    line-height: 1.5;
}

.hidden {
    display: none;
}

/* Container */
.identity-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: transparent;
}

/* Main Card */
.identity-card {
    width: 420px;
    background: #2a2a2a;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.6), 0 0 0 1px rgba(255, 255, 255, 0.1);
    border: 1px solid #404040;
    overflow: hidden;
    animation: slideIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
}

.identity-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-30px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Header */
.card-header {
    background: linear-gradient(135deg, #1e1e1e 0%, #2a2a2a 100%);
    padding: 24px;
    text-align: center;
    border-bottom: 1px solid #404040;
}

.logo-container {
    margin-bottom: 16px;
}

.logo-circle {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);
}

.logo-icon {
    color: #ffffff;
    font-size: 24px;
    line-height: 1;
}

.title {
    font-size: 20px;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 6px;
    letter-spacing: 0.8px;
}

.subtitle {
    font-size: 13px;
    color: #a0a0a0;
    font-weight: 400;
}

/* Body */
.card-body {
    padding: 24px;
}

/* Form */
.form {
    display: flex;
    flex-direction: column;
    gap: 18px;
}

/* Form Groups */
.form-group {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.form-group label {
    font-size: 13px;
    font-weight: 500;
    color: #e0e0e0;
    margin-bottom: 2px;
}

/* Inputs */
.form-group input {
    padding: 10px 14px;
    background: #1a1a1a;
    border: 1px solid #404040;
    border-radius: 6px;
    color: #ffffff;
    font-size: 13px;
    font-family: inherit;
    transition: all 0.2s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #4a90e2;
    background: #1f1f1f;
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.15);
}

.form-group input::placeholder {
    color: #707070;
}

/* Gender Selection */
.gender-options {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin-top: 4px;
}

.gender-option input {
    display: none;
}

.gender-label {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 12px 16px;
    background: #1a1a1a;
    border: 1px solid #404040;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
}

.gender-label:hover {
    border-color: #4a90e2;
    background: #1f1f1f;
}

.gender-option input:checked + .gender-label {
    border-color: #4a90e2;
    background: #1f1f1f;
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.15);
}

.gender-text {
    font-size: 13px;
    font-weight: 500;
    color: #e0e0e0;
}

/* Form Actions */
.form-actions {
    display: flex;
    gap: 12px;
    margin-top: 20px;
}

.btn {
    flex: 1;
    padding: 12px 20px;
    border: none;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 600;
    font-family: inherit;
    cursor: pointer;
    transition: all 0.2s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-cancel {
    background: #404040;
    color: #ffffff;
}

.btn-cancel:hover {
    background: #4a4a4a;
    transform: translateY(-1px);
}

.btn-confirm {
    background: #4a90e2;
    color: #ffffff;
}

.btn-confirm:hover {
    background: #357abd;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);
}

.btn:active {
    transform: translateY(0);
}

/* Error Messages */
.error {
    color: #ff6b6b;
    font-size: 11px;
    margin-top: 2px;
    display: none;
}

.error.show {
    display: block;
}

/* Responsive */
@media (max-width: 480px) {
    .identity-card {
        width: 95%;
        margin: 20px;
    }

    .gender-options {
        grid-template-columns: 1fr;
    }

    .form-actions {
        flex-direction: column;
    }
}
