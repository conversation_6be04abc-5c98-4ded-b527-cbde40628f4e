local ESX = nil
local spawnedPed = nil
local pedCoords = nil
local pedDamage = 0
local maxDamage = 100
local lastShotTime = 0

-- Initialize ESX
Citizen.CreateThread(function()
    while ESX == nil do
        TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)
        Citizen.Wait(0)
    end
end)

-- Register the spawn command
RegisterCommand('pedspawn', function(source, args, rawCommand)
    ESX.TriggerServerCallback('pedspawn:checkPermission', function(hasPermission)
        if hasPermission then
            SpawnPed()
        else
            lib.notify({
                title = 'Access Denied',
                description = 'You do not have permission to use this command.',
                type = 'error'
            })
        end
    end)
end, false)

-- Register the despawn command
RegisterCommand('peddespawn', function(source, args, rawCommand)
    ESX.TriggerServerCallback('pedspawn:checkPermission', function(hasPermission)
        if hasPermission then
            RemovePed()
        else
            lib.notify({
                title = 'Access Denied',
                description = 'You do not have permission to use this command.',
                type = 'error'
            })
        end
    end)
end, false)

-- Function to spawn the ped
function SpawnPed()
    -- Delete existing ped if it exists
    if spawnedPed and DoesEntityExist(spawnedPed) then
        DeleteEntity(spawnedPed)
    end
    
    -- Get player coordinates
    local playerPed = PlayerPedId()
    local coords = GetEntityCoords(playerPed)
    
    -- Load the ped model
    local pedModel = GetHashKey('ig_trafficwarden')
    
    RequestModel(pedModel)
    while not HasModelLoaded(pedModel) do
        Citizen.Wait(1)
    end
    
    -- Spawn the ped
    spawnedPed = CreatePed(4, pedModel, coords.x, coords.y, coords.z, 0.0, false, true)
    pedCoords = coords
    pedDamage = 0
    
    -- Set ped properties (NOT invincible so we can detect damage)
    SetEntityHealth(spawnedPed, 200)
    SetPedMaxHealth(spawnedPed, 200)
    SetEntityInvincible(spawnedPed, false)
    SetPedCanRagdoll(spawnedPed, false)
    SetPedCanRagdollFromPlayerImpact(spawnedPed, false)
    
    -- Make ped not scared and not run away
    SetPedFleeAttributes(spawnedPed, 0, 0)
    SetPedCombatAttributes(spawnedPed, 17, 1)
    SetPedCombatAttributes(spawnedPed, 46, 1)
    SetPedCombatAttributes(spawnedPed, 5, 1)
    SetPedCombatAttributes(spawnedPed, 1, 1)
    SetBlockingOfNonTemporaryEvents(spawnedPed, true)
    
    -- Free the model
    SetModelAsNoLongerNeeded(pedModel)
    
    -- Show notification
    lib.notify({
        title = 'Ped Spawned',
        description = 'Traffic Warden ped has been spawned at your location.',
        type = 'success'
    })
end

-- Function to remove the ped
function RemovePed()
    if spawnedPed and DoesEntityExist(spawnedPed) then
        DeleteEntity(spawnedPed)
        spawnedPed = nil
        pedCoords = nil
        pedDamage = 0
        
        lib.notify({
            title = 'Ped Removed',
            description = 'Traffic Warden ped has been removed.',
            type = 'success'
        })
    else
        lib.notify({
            title = 'No Ped Found',
            description = 'There is no ped to remove.',
            type = 'error'
        })
    end
end

-- Function to revive the ped at the same location
function RevivePed()
    if pedCoords then
        -- Load the ped model
        local pedModel = GetHashKey('ig_trafficwarden')
        
        RequestModel(pedModel)
        while not HasModelLoaded(pedModel) do
            Citizen.Wait(1)
        end
        
        -- Spawn the ped at the same location
        spawnedPed = CreatePed(4, pedModel, pedCoords.x, pedCoords.y, pedCoords.z, 0.0, false, true)
        pedDamage = 0
        
        -- Set ped properties (NOT invincible so we can detect damage)
        SetEntityHealth(spawnedPed, 200)
        SetPedMaxHealth(spawnedPed, 200)
        SetEntityInvincible(spawnedPed, false)
        SetPedCanRagdoll(spawnedPed, false)
        SetPedCanRagdollFromPlayerImpact(spawnedPed, false)
        
        -- Make ped not scared and not run away
        SetPedFleeAttributes(spawnedPed, 0, 0)
        SetPedCombatAttributes(spawnedPed, 17, 1)
        SetPedCombatAttributes(spawnedPed, 46, 1)
        SetPedCombatAttributes(spawnedPed, 5, 1)
        SetPedCombatAttributes(spawnedPed, 1, 1)
        SetBlockingOfNonTemporaryEvents(spawnedPed, true)
        
        -- Free the model
        SetModelAsNoLongerNeeded(pedModel)
        
        -- Show notification
        lib.notify({
            title = 'Ped Revived',
            description = 'Traffic Warden ped has been revived.',
            type = 'success'
        })
    end
end

-- Function to calculate distance
function GetDistance(coords1, coords2)
    return math.floor(#(coords1 - coords2))
end

-- Monitor ped damage and handle death/revive
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(50)
        
        if spawnedPed and DoesEntityExist(spawnedPed) then
            local playerPed = PlayerPedId()
            local playerCoords = GetEntityCoords(playerPed)
            local pedPos = GetEntityCoords(spawnedPed)
            local currentHealth = GetEntityHealth(spawnedPed)
            
            -- Check if ped health has decreased (been hit)
            if currentHealth < 200 then
                local currentTime = GetGameTimer()
                
                -- Prevent multiple detections of the same shot
                if currentTime - lastShotTime > 200 then
                    lastShotTime = currentTime
                    pedDamage = pedDamage + 10
                    
                    -- Calculate hit distance from player to ped
                    local hitDistance = GetDistance(playerCoords, pedPos)
                    
                    -- Send chat message with hit distance
                    TriggerEvent('chat:addMessage', {
                        args = {
                            '^1^*PED^r^7 You hit the ped from ' .. hitDistance .. ' meters away.'
                        }
                    })
                    
                    -- Show damage notification
                    lib.notify({
                        title = 'Damage Dealt',
                        description = 'Damage: ' .. pedDamage .. '/' .. maxDamage,
                        type = 'info'
                    })
                    
                    -- Reset ped health to full
                    SetEntityHealth(spawnedPed, 200)
                    
                    -- Check if ped should "die"
                    if pedDamage >= maxDamage then
                        -- Send kill message
                        TriggerEvent('chat:addMessage', {
                            args = {
                                '^1^*PED^r^7 You killed the ped! It will revive in 2 seconds.'
                            }
                        })
                        
                        -- Delete the ped and revive after delay
                        DeleteEntity(spawnedPed)
                        
                        -- Wait 2 seconds then revive
                        Citizen.Wait(2000)
                        RevivePed()
                    end
                end
            end
        end
    end
end)

-- Clean up on resource stop
AddEventHandler('onResourceStop', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        if spawnedPed and DoesEntityExist(spawnedPed) then
            DeleteEntity(spawnedPed)
        end
    end
end)