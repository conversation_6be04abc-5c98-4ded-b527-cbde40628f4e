Config                    = {}
Config.Locale             = GetConvar('esx:locale', 'en')

-- [Config.EnableCommands]
-- Enables Commands Such As /char and /chardel
Config.EnableCommands     = ESX.GetConfig().EnableDebug

-- EXPERIMENTAL Character Registration Method
Config.UseDeferrals       = false

-- These values are for the date format in the registration menu
-- Choices: DD/MM/YYYY | MM/DD/YYYY | YYYY/MM/DD
Config.DateFormat         = 'DD/MM/YYYY'

-- These values are for the second input validation in server/main.lua
Config.MinFirstNameLength = 2                           -- Min First Name Length.
Config.MaxFirstNameLength = 50                          -- Max First Name Length.
Config.MinLastNameLength  = 2                           -- Min Last Name Length.
Config.MaxLastNameLength  = 50                          -- Max Last Name Length.
Config.MinHeight          = -999999                     -- No minimum height limit
Config.MaxHeight          = 999999                      -- No maximum height limit
Config.LowestYear         = 1                           -- No age restrictions
Config.HighestYear        = 9999                        -- No age restrictions

Config.FullCharDelete     = true                        -- Delete all reference to character.
Config.EnableDebugging    = ESX.GetConfig().EnableDebug -- prints for debugging :)
