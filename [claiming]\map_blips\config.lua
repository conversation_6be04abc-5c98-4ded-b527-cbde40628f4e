-- config.lua

Config = {}

-- Position & size of the bar, relative to screen (0.0–1.0)
Config.Bar = {
  width  = 0.1399999,   -- 20% of screen width
  height = 0.0270,  -- 1.8% of screen height
  x      = 0.085,   -- center X (0.0 = left, 1.0 = right)
  y      = 0.98199   -- center Y (0.0 = top, 1.0 = bottom)
}

-- Colors (RGBA 0–255)
Config.Colors = {
  background = {150, 0,   0,   150},  -- dark red, semi-transparent
  foreground = {255, 0,   0,   200}   -- bright red
}

-- Hide the default health HUD component? (true/false)
Config.HideDefault = true
