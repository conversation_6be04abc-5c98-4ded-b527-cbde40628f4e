<html>
    <head>
        <meta charset="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
        <link rel="stylesheet" type="text/css" href="css/main.css" />
        <script src="locales/en.js"></script>
    </head>
    <body style="display: none">
        <!-- Custom character selection overlay -->
        <div class="character-overlay">
            <!-- Character list panel - Far left -->
            <div class="character-list-panel">
                <div class="panel-header">Characters</div>
                <div id="character-list"></div>
            </div>

            <!-- Character info panel - Far right -->
            <div class="character-info-panel">
                <div class="panel-header">Character Info</div>
                <div id="character-info">
                    <div class="no-selection">Select a character to view details</div>
                </div>
                <button id="select-btn" style="display: none;">Select Character</button>
            </div>
        </div>

        <!-- Hide esx_context overlay -->
        <style>
            /* Hide esx_context menu */
            .esx-context-menu,
            .context-menu,
            [class*="context"] {
                display: none !important;
                visibility: hidden !important;
            }
        </style>

        <script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
        <script src="js/app.js"></script>
    </body>
</html>
