ESX = nil
TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)

local lastPositions = {}

AddEventHandler('playerDropped', function()
  local src = source
  local xP = ESX.GetPlayerFromId(src)
  if xP then
    lastPositions[xP.identifier] = GetEntityCoords(GetPlayerPed(src))
  end
end)

ESX.RegisterServerCallback('spawnselector:getLastPosition', function(source, cb)
  local xPlayer = ESX.GetPlayerFromId(source)
  if not xPlayer then return cb(nil) end

  local identifier = xPlayer.getIdentifier()

  MySQL.Async.fetchAll('SELECT position FROM users WHERE identifier = @identifier', {
    ['@identifier'] = identifier
  }, function(result)
    if result[1] and result[1].position then
      local pos = json.decode(result[1].position)
      cb(pos)
    else
      cb(nil) -- No saved position = first ever time in server
    end
  end)
end)
