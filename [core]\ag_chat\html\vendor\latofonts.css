/* latin-ext */

@font-face {

  font-family: 'Lato';

  font-style: normal;

  font-weight: 300;

  src: local('Lato Light'), local('Lato-Light'), url(fonts/LatoLight.woff2);

  unicode-range: U+0100-024F, U+1E00-1EFF, U+20A0-20AB, U+20AD-20CF, U+2C60-2C7F, U+A720-A7FF;

}

/* latin */

@font-face {

  font-family: 'Lato';

  font-style: normal;

  font-weight: 300;

  src: local('Lato Light'), local('Lato-Light'), url(fonts/LatoLight2.woff2);

  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2212, U+2215;

}

/* latin-ext */

@font-face {

  font-family: 'Lato';

  font-style: normal;

  font-weight: 400;

  src: local('Lato Regular'), local('Lato-Regular'), url(fonts/LatoRegular.woff2);

  unicode-range: U+0100-024F, U+1E00-1EFF, U+20A0-20AB, U+20AD-20CF, U+2C60-2C7F, U+A720-A7FF;

}

/* latin */


/* latin-ext */

@font-face {

  font-family: 'Lato';

  font-style: normal;

  font-weight: 700;

  src: local('Lato Bold'), local('Lato-Bold'), url(fonts/LatoBold.woff2);

  unicode-range: U+0100-024F, U+1E00-1EFF, U+20A0-20AB, U+20AD-20CF, U+2C60-2C7F, U+A720-A7FF;

}
