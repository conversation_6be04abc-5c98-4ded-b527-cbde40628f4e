ESX = nil

-- State variables
local logs           = {}
local active         = false
local paused         = false
local pausedRemaining= 0
local isPaused        = false
local roundEndTime    = 0    -- game-timer ms when the round should finish
local remainingMillis = 0    -- stored when paused
local ROUND_MS
local localClaimed   = false
local claimedByJob   = nil -- Track which job claimed the location
local roundCount     = 0
local roundEndTime   = 0
local currentMode    = 'any'
local currentLabel   = 'N/A'
local currentCreator = 'N/A'
local currentLoc     = nil
local locCoords      = vector3(0,0,0)
local blipRadius, blipSkull
local autoEndTriggered = false -- Flag to prevent multiple auto-end triggers
local siphonEnabled = false -- Track siphon state
local heliModeEnabled = false -- Track standalone heli mode state

-- Marker settings from config
local markerType  = Config.MarkerType
local markerSize  = Config.MarkerSize
local redColor    = {r=255, g=0,   b=0,   a=175}
local greenColor  = {r=0,   g=255, b=0,   a=175}

-- Helper to format milliseconds to MM:SS
local function formatTime(ms)
  if ms < 0 then ms = 0 end
  local m = math.floor(ms / 60000)
  local s = math.floor((ms % 60000) / 1000)
  return string.format("%02d:%02d", m, s)
end

-- Draw 3D text at world coordinates
local function DrawText3D(x,y,z,text)
  local onScreen, _x, _y = World3dToScreen2d(x, y, z)
  if not onScreen then return end
  SetTextScale(0.35, 0.35)
  SetTextFont(4)
  SetTextProportional(1)
  SetTextEntry("STRING")
  SetTextCentre(true)
  SetTextDropShadow(1, 0, 0, 0, 255)
  SetTextOutline()
  AddTextComponentSubstringPlayerName(text)
  DrawText(_x, _y)
end

-- Initialize ESX
Citizen.CreateThread(function()
  while ESX == nil do
    TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)
    Citizen.Wait(0)
  end
end)

-- NUI callbacks for UI buttons
RegisterNUICallback('start', function(data, cb)
  TriggerServerEvent('void_claiming:startRound', data.location, data.mode)
  cb('ok')
end)
RegisterNUICallback('end', function(_, cb)
  TriggerServerEvent('void_claiming:endRound')
  cb('ok')
end)
RegisterNUICallback('pause', function(_, cb)
  TriggerServerEvent('void_claiming:pauseRound')
  cb('ok')
end)
RegisterNUICallback('closeUI', function(_, cb)
  SetNuiFocus(false, false)
  cb('ok')
end)
RegisterNUICallback('toggleCarWarfareClient', function(data, cb)
  print('[CLAIMING] Car warfare button clicked - toggling client-side')
  -- Directly toggle client-side car warfare (like the working test command)
  TriggerEvent('claiming:toggleCarWarfareClient')
  cb('ok')
end)
RegisterNUICallback('goToLocation', function(data, cb)
  local location = data.location
  local coords, targetLabel

  -- Debug current state
  print("Go To Location Debug - Active: " .. tostring(active) .. ", Paused: " .. tostring(paused))
  print("Go To Location Debug - CurrentLoc: " .. tostring(currentLoc) .. ", LocCoords: " .. tostring(locCoords))

  -- PRIORITY: If there's an active round, ALWAYS teleport to the active vendor location
  if (active or paused) and currentLoc and locCoords then
    coords = locCoords
    targetLabel = currentLabel .. " (Active Round)"
    print("Go To Location Debug - Using active round location: " .. currentLabel)
    print("Go To Location Debug - Active round coords: " .. tostring(coords))
  else
    -- No active round - show notification and exit
    ESX.ShowNotification('~r~No active round')
    cb('ok')
    return
  end

  local x, y, z

  -- Handle different coordinate formats safely
  if coords then
    -- Try to extract coordinates from vector3/vector4
    if type(coords.x) == "number" and type(coords.y) == "number" and type(coords.z) == "number" then
      x, y, z = coords.x, coords.y, coords.z
      print("Go To Location Debug - Extracted coords: " .. x .. ", " .. y .. ", " .. z)
    else
      -- Fallback for other formats
      local coordsTable = {coords.x, coords.y, coords.z}
      if coordsTable[1] and coordsTable[2] and coordsTable[3] then
        x, y, z = coordsTable[1], coordsTable[2], coordsTable[3]
        print("Go To Location Debug - Fallback coords: " .. x .. ", " .. y .. ", " .. z)
      end
    end
  end

  -- Final validation and teleport
  if x and y and z and type(x) == "number" and type(y) == "number" and type(z) == "number" then
    SetEntityCoords(PlayerPedId(), x, y, z, false, false, false, true)
    ESX.ShowNotification('Teleported to ' .. targetLabel)
  else
    ESX.ShowNotification('Error: Invalid coordinates for this location!')
  end

  cb('ok')
end)

-- Open and initialize the UI
RegisterNetEvent('void_claiming:openUI')
AddEventHandler('void_claiming:openUI', function()
  local locList = {}
  local modeList = {
    { key = 'any',        label = 'Any' },
    { key = 'deagleonly', label = 'Deagle Only' },
    { key = 'x2headdy',   label = 'x2 Headdy' }
  }
  for k,v in pairs(Config.Locations) do
    table.insert(locList, { key = k, label = v.label })
  end

  -- Prepare live status snapshot
  local liveStatus = {
    location = (active or paused) and currentLabel or 'N/A',
    mode     = active and currentMode or 'N/A',
    creator  = active and currentCreator or 'N/A'
  }

  -- Send initialization data to UI
  SendNUIMessage({
    action     = 'init',
    locations  = locList,
    modes      = modeList,
    logs       = logs,
    liveStatus = liveStatus
  })

  SetNuiFocus(true, true)
  SendNUIMessage({ action = 'openUI' })
end)

-- Handle new log entries and insert newest first
RegisterNetEvent('void_claiming:newLog')
AddEventHandler('void_claiming:newLog', function(name, group, status, mode, locLabel)
  local entry = {
    name     = name,
    group    = group,
    status   = status,
    mode     = mode,
    location = locLabel
  }
  table.insert(logs, 1, entry)
  SendNUIMessage({ action = 'addLog', entry = entry })
end)

-- Remove map blips when instructed
RegisterNetEvent('claiming:clearBlips')
AddEventHandler('claiming:clearBlips', function()
  if blipRadius then RemoveBlip(blipRadius); blipRadius = nil end
  if blipSkull  then RemoveBlip(blipSkull);  blipSkull  = nil end
end)

-- Update game mode
RegisterNetEvent('claiming:mode')
AddEventHandler('claiming:mode', function(mode)
  currentMode = mode
end)

-- Start a claiming round: setup state, create blips, update UI
RegisterNetEvent('claiming:start')
AddEventHandler('claiming:start', function(locKey, coords, mode)
  print("[CLAIMING] Client received claiming:start event for location: " .. locKey .. ", mode: " .. (mode or 'any'))
  active         = true
  paused         = false
  localClaimed   = false
  claimedByJob   = nil -- Reset claimed job
  pausedRemaining= 0
  currentLoc     = locKey
  currentMode    = mode or 'any' -- Store the mode
  roundCount     = roundCount + 1
  roundEndTime   = GetGameTimer() + Config.ClaimDuration
  locCoords      = vector3(coords.x, coords.y, coords.z)
  currentLabel   = Config.Locations[locKey].label
  currentCreator = GetPlayerName(PlayerId())
  autoEndTriggered = false -- Reset auto-end flag for new round
  print("[CLAIMING] Round started - initial roundEndTime set to: " .. roundEndTime)

  -- Draw radius blip
  blipRadius = AddBlipForRadius(coords.x, coords.y, coords.z, Config.Radius)
  SetBlipColour(blipRadius, 1)
  SetBlipAlpha(blipRadius, 64)

  -- Draw skull blip
  blipSkull = AddBlipForCoord(coords.x, coords.y, coords.z)
  SetBlipSprite(blipSkull, Config.SkullSprite)
  SetBlipColour(blipSkull, 1)
  BeginTextCommandSetBlipName('STRING')
  AddTextComponentString('Claim Zone #' .. roundCount)
  EndTextCommandSetBlipName(blipSkull)

  -- Update live status in UI
  SendNUIMessage({
    action     = 'updateLive',
    liveStatus = {
      location = currentLabel,
      mode     = currentMode,
      creator  = currentCreator
    }
  })
end)

-- Mark as claimed on clients
RegisterNetEvent('claiming:claimed')
AddEventHandler('claiming:claimed', function(locKey, jobName, jobLabel)
  if locKey == currentLoc then
    claimedByJob = jobName

    -- Check if this player's job is the one that claimed it
    ESX.TriggerServerCallback('esx:getPlayerData', function(playerData)
      if playerData and playerData.job then
        local myJobName = playerData.job.name
        localClaimed = (myJobName == jobName)
      end
    end)
  end
end)

-- End a claiming round: clear state and blips
RegisterNetEvent('claiming:end')
AddEventHandler('claiming:end', function()
  active = false
  paused = false
  roundEndTime = 0
  localClaimed = false
  claimedByJob = nil -- Reset claimed job
  currentMode = 'any' -- Reset mode
  TriggerEvent('claiming:clearBlips')
end)

-- Kill all players when round ends
RegisterNetEvent('claiming:killAllPlayers')
AddEventHandler('claiming:killAllPlayers', function()
  local playerPed = PlayerPedId()
  SetEntityHealth(playerPed, 0)
end)

-- Toggle pause/unpause
RegisterNetEvent('claiming:pause')
AddEventHandler('claiming:pause', function()
  if active and not paused then
    -- Pause the round
    paused = true
    pausedRemaining = roundEndTime - GetGameTimer()
    if pausedRemaining < 0 then pausedRemaining = 0 end
  elseif paused then
    -- Resume the round
    paused = false
    active = true
    roundEndTime = GetGameTimer() + pausedRemaining
    pausedRemaining = 0
  end
end)

-- Draw marker, timer, and prompts each frame when active or paused
Citizen.CreateThread(function()
  while true do
    Citizen.Wait((active or paused) and 0 or 500)
    if active or paused then
      -- Determine marker color
      local col = localClaimed and greenColor or redColor
      -- Draw the 3D marker at the configured location
      DrawMarker(markerType,
        locCoords.x, locCoords.y, locCoords.z - 0.98,
        0, 0, 0, 0, 0, 0,
        markerSize.x, markerSize.y, markerSize.z,
        col.r, col.g, col.b, col.a,
        false, true, 2, nil, nil, false
      )
      -- Draw claim prompt or claimed text when player is near
      local ped = PlayerPedId()
      local pos = GetEntityCoords(ped)
      if #(pos - locCoords) < 2.0 then
        if claimedByJob then
          -- Check if player's job is the one that claimed it
          local playerData = ESX.GetPlayerData()
          if playerData and playerData.job and playerData.job.name == claimedByJob then
            DrawText3D(
              locCoords.x, locCoords.y, locCoords.z + markerSize.z - 1.0,
              "~g~CLAIMED BY YOUR JOB"
            )
          else
            DrawText3D(
              locCoords.x, locCoords.y, locCoords.z + markerSize.z - 1.0,
              "[~g~G~w~] Claim for Your Job"
            )
            if IsControlJustReleased(0, 47) then
              TriggerServerEvent('claiming:attempt', currentLoc)
            end
          end
        else
          DrawText3D(
            locCoords.x, locCoords.y, locCoords.z + markerSize.z - 1.0,
            "[~g~G~w~] Claim Vendor"
          )
          if IsControlJustReleased(0, 47) then
            TriggerServerEvent('claiming:attempt', currentLoc)
          end
        end
      end
    end
  end
end)

-- Enhanced HUD banner with better styling and status indicators
Citizen.CreateThread(function()
  local labels = { any = 'Any Weapons', deagleonly = 'Deagle Only', x2headdy = 'x2 Headdy' }
  while true do
    Citizen.Wait(0)
    if roundEndTime > 0 or paused then
      local timeRemaining = paused and pausedRemaining or roundEndTime - GetGameTimer()

      -- Auto-end round when timer hits 0 (only trigger once)
      if not paused and timeRemaining <= 0 and active and not autoEndTriggered then
        autoEndTriggered = true
        TriggerServerEvent('void_claiming:endRound')
      end

      local statusColor = paused and "~y~PAUSED" or (localClaimed and "~g~CLAIMED" or "~r~ACTIVE")
      local timeColor = timeRemaining < 60000 and "~r~" or "~b~"

      -- Format main text (only mode, round, time)
      local roundNumber = currentLoc or roundCount
      local txt = string.format(
        "Mode: ~b~%s~w~ | Round: ~b~#%s~w~ | Time: %s%s",
        labels[currentMode] or currentMode, roundNumber,
        timeColor, formatTime(timeRemaining)
      )

      -- Main HUD text
      SetTextFont(4)
      SetTextScale(0.40, 0.40)
      SetTextCentre(false)
      SetTextOutline()
      SetTextEntry("STRING")
      AddTextComponentSubstringPlayerName(txt)
      DrawText(0.015, 0.78)
    end
  end
end)

-- Car warfare state update
RegisterNetEvent('claiming:setCarWarfare')
AddEventHandler('claiming:setCarWarfare', function(enabled)
  -- Update UI button state
  SendNUIMessage({
    action = 'updateCarWarfare',
    enabled = enabled
  })
end)

-- Event to handle siphon state from server
RegisterNetEvent('claiming:setSiphon')
AddEventHandler('claiming:setSiphon', function(enabled)
    siphonEnabled = enabled
    print('[CLAIMING] Siphon ' .. (enabled and 'enabled' or 'disabled'))
end)

-- Event to handle heli mode state from server
RegisterNetEvent('claiming:setHeliMode')
AddEventHandler('claiming:setHeliMode', function(enabled)
    heliModeEnabled = enabled
    print('[CLAIMING] Heli mode ' .. (enabled and 'enabled' or 'disabled'))
end)



-- Request sync when player loads (for players joining during active rounds)
Citizen.CreateThread(function()
  while ESX == nil do
    TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)
    Citizen.Wait(0)
  end

  -- Wait a bit more for everything to load
  Citizen.Wait(2000)

  -- Request sync from server
  print('[CLAIMING] Client requesting sync from server')
  TriggerServerEvent('claiming:requestSync')

  -- Also notify server that player has connected
  TriggerServerEvent('claiming:playerConnected')
end)

-- Additional sync request on spawn
AddEventHandler('playerSpawned', function()
  Citizen.Wait(1000) -- Small delay
  print('[CLAIMING] Player spawned - requesting sync')
  TriggerServerEvent('claiming:requestSync')
end)


RegisterNetEvent("claiming:setTimer")
AddEventHandler("claiming:setTimer", function(timeLeft)
  local oldEndTime = roundEndTime
  roundEndTime = GetGameTimer() + timeLeft
  print("[CLAIMING] Synced timer from server with " .. math.floor(timeLeft / 1000) .. " seconds left.")
  print("[CLAIMING] Old roundEndTime: " .. oldEndTime .. ", New roundEndTime: " .. roundEndTime)
  print("[CLAIMING] Current GameTimer: " .. GetGameTimer())
end)

-- Debug command to manually request sync
RegisterCommand('syncclaim', function()
  print('[CLAIMING] Manual sync requested')
  TriggerServerEvent('claiming:requestSync')
end, false)
