* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: transparent;
    overflow: hidden;
}

.hidden {
    display: none !important;
}

#range-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
}

.range-panel {
    width: 50vw;
    max-width: 600px;
    min-width: 400px;
    background: linear-gradient(145deg, #2a2a2a, #1a1a1a);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    border: 2px solid #333;
    overflow: hidden;
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.header {
    background: linear-gradient(135deg, #1e3c72, #2a5298);
    padding: 20px;
    text-align: center;
    border-bottom: 2px solid #333;
}

.title {
    color: #4A90E2;
    font-size: 28px;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    margin: 0;
}

.content {
    padding: 30px;
}

.range-selection {
    margin-bottom: 30px;
    text-align: center;
}

.range-selection label {
    display: block;
    color: #ffffff;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 15px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

#range-select {
    width: 200px;
    padding: 12px 16px;
    font-size: 16px;
    background: #333;
    color: #ffffff;
    border: 2px solid #555;
    border-radius: 10px;
    outline: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

#range-select:hover {
    border-color: #4A90E2;
    background: #404040;
}

#range-select:focus {
    border-color: #4A90E2;
    box-shadow: 0 0 10px rgba(74, 144, 226, 0.3);
}

.button-container {
    display: flex;
    justify-content: center;
    gap: 20px;
}

.btn {
    padding: 12px 30px;
    font-size: 16px;
    font-weight: 600;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}

.btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.btn-confirm {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
}

.btn-confirm:hover {
    background: linear-gradient(135deg, #45a049, #3d8b40);
}

.btn-end {
    background: linear-gradient(135deg, #f44336, #d32f2f);
    color: white;
}

.btn-end:hover {
    background: linear-gradient(135deg, #d32f2f, #b71c1c);
}
